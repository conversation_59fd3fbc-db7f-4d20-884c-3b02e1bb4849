<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DENR Control Map - Configuration</title>
    <!-- Prevent favicon 500 errors by providing a data URL favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚙️</text></svg>">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 5px solid #4CAF50;
        }
        
        .section h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.3em;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
            font-size: 0.95em;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        
        .buttons {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 8px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }
        
        .btn.secondary {
            background: #2196F3;
        }
        
        .btn.secondary:hover {
            background: #1976D2;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }
        
        .btn.warning {
            background: #ff9800;
        }
        
        .btn.warning:hover {
            background: #f57c00;
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
        }
        
        .btn.danger {
            background: #f44336;
        }
        
        .btn.danger:hover {
            background: #d32f2f;
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
        }
        
        .preview-section {
            background: #e8f5e8;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .preview-section h4 {
            margin-top: 0;
            color: #2e7d32;
        }
        
        .status-message {
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: 500;
            display: none;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .help-text {
            font-size: 0.85em;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .navigation {
            background: #34495e;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .navigation a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background 0.3s ease;
        }
        
        .navigation a:hover {
            background: rgba(255,255,255,0.1);
        }
        
        @media (max-width: 768px) {
            .grid-2, .grid-3 {
                grid-template-columns: 1fr;
            }
            
            body {
                padding: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="navigation">
            <div>
                <a href="index.html">← Back to Map</a>
            </div>
            <div>
                <span style="color: #bdc3c7;">ERDB Control Map Configuration</span>
            </div>
        </div>
        
        <div class="header">
            <h1>⚙️ Configuration Manager</h1>
            <p>Customize your ERDB Control Map settings</p>
        </div>
        
        <div class="content">
            <div id="status-message" class="status-message"></div>
            
            <div class="section">
                <h3>🏢 Application Settings</h3>
                <div class="grid-2">
                    <div class="form-group">
                        <label for="cfg-title">Application Title</label>
                        <input type="text" id="cfg-title" placeholder="ERDB Control Map">
                        <div class="help-text">This appears in the browser tab and application header</div>
                    </div>
                    <div class="form-group">
                        <label for="cfg-description">Description</label>
                        <input type="text" id="cfg-description" placeholder="Application description">
                        <div class="help-text">Shown in the navigation area</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>🗺️ Map Configuration</h3>
                <div class="grid-3">
                    <div class="form-group">
                        <label for="cfg-center-lng">Center Longitude</label>
                        <input type="number" id="cfg-center-lng" step="0.000001" placeholder="122.0">
                        <div class="help-text">East/West position</div>
                    </div>
                    <div class="form-group">
                        <label for="cfg-center-lat">Center Latitude</label>
                        <input type="number" id="cfg-center-lat" step="0.000001" placeholder="12.0">
                        <div class="help-text">North/South position</div>
                    </div>
                    <div class="form-group">
                        <label for="cfg-zoom">Zoom Level</label>
                        <input type="number" id="cfg-zoom" min="1" max="20" placeholder="6">
                        <div class="help-text">1 = World view, 20 = Street level</div>
                    </div>
                </div>
                <div class="grid-2">
                    <div class="form-group">
                        <label for="cfg-basemap">Default Basemap</label>
                        <select id="cfg-basemap">
                            <option value="topo">Topographic</option>
                            <option value="streets">Streets</option>
                            <option value="satellite">Satellite</option>
                            <option value="hybrid">Hybrid</option>
                            <option value="terrain">Terrain</option>
                            <option value="osm">OpenStreetMap</option>
                            <option value="dark-gray">Dark Gray</option>
                            <option value="gray">Gray</option>
                        </select>
                        <div class="help-text">Background map style</div>
                    </div>
                    <div class="form-group">
                        <label for="cfg-alt-basemap">Toggle Basemap</label>
                        <select id="cfg-alt-basemap">
                            <option value="satellite">Satellite</option>
                            <option value="topo">Topographic</option>
                            <option value="streets">Streets</option>
                            <option value="hybrid">Hybrid</option>
                            <option value="terrain">Terrain</option>
                            <option value="osm">OpenStreetMap</option>
                            <option value="dark-gray">Dark Gray</option>
                            <option value="gray">Gray</option>
                        </select>
                        <div class="help-text">Alternate basemap for toggle button</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>🌐 Service Configuration</h3>
                <div class="form-group">
                    <label for="cfg-portal-url">Portal URL</label>
                      <input type="url" id="cfg-portal-url" placeholder="https://controlmap.denr.gov.ph/arcgis">
                    <div class="help-text">
                        Your ArcGIS Portal URL (without /home suffix)<br>
                        <strong>Correct format:</strong> https://controlmap.denr.gov.ph/arcgis<br>
                        <strong>Incorrect format:</strong> https://controlmap.denr.gov.ph/arcgis/home
                    </div>
                </div>
                <div class="form-group">
                    <label for="cfg-feature-url">Feature Layer URL</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="url" id="cfg-feature-url" placeholder="https://server/rest/services/layer/FeatureServer" style="flex: 1;">
                        <button type="button" class="btn secondary" onclick="discoverFields()" id="discover-btn" style="margin: 0; white-space: nowrap;">🔍 Auto-Discover Fields</button>
                    </div>
                    <div class="help-text">URL to your feature service for data display and editing</div>
                    <div id="discovery-status" style="margin-top: 10px; padding: 10px; border-radius: 4px; display: none;"></div>
                </div>
            </div>

            <div class="section">
                <h3>🔐 Authentication</h3>
                <div class="form-group">
                    <label for="cfg-require-auth">
                        <input type="checkbox" id="cfg-require-auth" style="width: auto; margin-right: 8px;">
                        Require Authentication for Feature Layer Access
                    </label>
                    <div class="help-text">Enable this if your feature layer is not publicly accessible</div>
                </div>
                
                <div id="auth-options" style="display: none;">
                    <div class="form-group">
                        <label for="cfg-auth-mode">Authentication Method</label>
                        <select id="cfg-auth-mode" onchange="toggleAuthFields()">
                            <option value="oauth">OAuth (Recommended)</option>
                            <option value="credentials">Username/Password</option>
                            <option value="token">Pre-generated Token</option>
                        </select>
                        <div class="help-text">Choose how to authenticate with the ArcGIS service</div>
                    </div>

                    <div id="oauth-fields">
                        <div class="form-group">
                            <label for="cfg-app-id">OAuth Application ID</label>
                            <input type="text" id="cfg-app-id" placeholder="uvfX7FFxhzf31s1Z">
                            <div class="help-text">OAuth App ID registered in your ArcGIS Portal</div>
                        </div>
                        <div class="form-group">
                            <label for="cfg-auto-signin">
                                <input type="checkbox" id="cfg-auto-signin" style="width: auto; margin-right: 8px;">
                                Auto Sign-in on Application Start
                            </label>
                            <div class="help-text">Automatically prompt for login when the application loads</div>
                        </div>
                    </div>

                    <div id="credentials-fields" style="display: none;">
                        <div class="grid-2">
                            <div class="form-group">
                                <label for="cfg-username">Username</label>
                                <input type="text" id="cfg-username" placeholder="Your ArcGIS username" autocomplete="username">
                                <div class="help-text">ArcGIS Portal username</div>
                            </div>
                            <div class="form-group">
                                <label for="cfg-password">Password</label>
                                <input type="password" id="cfg-password" placeholder="Your ArcGIS password" autocomplete="current-password">
                                <div class="help-text">ArcGIS Portal password</div>
                            </div>
                        </div>
                        <div class="form-group" style="background: #fff3cd; padding: 10px; border-radius: 4px; border: 1px solid #ffeaa7;">
                            <strong>⚠️ Security Note:</strong> Username and password are stored locally in your browser. 
                            For production use, OAuth authentication is recommended for better security.
                        </div>
                    </div>

                    <div id="token-fields" style="display: none;">
                        <div class="form-group">
                            <label for="cfg-token">Authentication Token</label>
                            <input type="text" id="cfg-token" placeholder="Your pre-generated authentication token">
                            <div class="help-text">Token generated from ArcGIS Portal or Server</div>
                        </div>
                        <div class="form-group">
                            <label for="cfg-token-expiration">Token Expiration (Optional)</label>
                            <input type="datetime-local" id="cfg-token-expiration">
                            <div class="help-text">When the token expires (leave blank if unknown)</div>
                        </div>
                        <div class="form-group">
                            <button type="button" class="btn secondary" onclick="testAuthentication()" id="test-auth-btn">🧪 Test Authentication</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>📐 Layout Settings</h3>
                <div class="grid-2">
                    <div class="form-group">
                        <label for="cfg-map-height">Map Height</label>
                        <input type="text" id="cfg-map-height" placeholder="70%" pattern="\d+%">
                        <div class="help-text">Percentage of screen for map (e.g., 70%)</div>
                    </div>
                    <div class="form-group">
                        <label for="cfg-table-height">Table Height</label>
                        <input type="text" id="cfg-table-height" placeholder="30%" pattern="\d+%">
                        <div class="help-text">Percentage of screen for data table (e.g., 30%)</div>
                    </div>
                </div>
            </div>

            <div class="section" id="field-discovery-section" style="display: none;">
                <h3>🔬 Discovered Layer Information</h3>
                <div id="layer-info-content">
                    <p><strong>Layer Name:</strong> <span id="layer-name">-</span></p>
                    <p><strong>Geometry Type:</strong> <span id="layer-geometry">-</span></p>
                    <p><strong>Field Count:</strong> <span id="field-count">-</span></p>
                    <p><strong>Capabilities:</strong> <span id="layer-capabilities">-</span></p>
                    <details style="margin-top: 15px;">
                        <summary style="cursor: pointer; font-weight: bold;">📊 Discovered Fields</summary>
                        <div id="discovered-fields-list" style="margin-top: 10px; max-height: 200px; overflow-y: auto;"></div>
                    </details>
                </div>
            </div>

            <div class="preview-section">
                <h4>📋 Current Configuration Preview</h4>
                <p><strong>Application:</strong> <span id="preview-title">ERDB Control Map</span></p>
                <p><strong>Map Center:</strong> <span id="preview-center">[122, 12]</span></p>
                <p><strong>Zoom:</strong> <span id="preview-zoom">6</span></p>
                <p><strong>Basemap:</strong> <span id="preview-basemap">Topographic</span></p>
                <p><strong>Portal:</strong> <span id="preview-portal">Default Portal</span></p>
                <p><strong>Table Columns:</strong> <span id="preview-columns">Default columns</span></p>
            </div>

            <div class="buttons">
                <button class="btn" onclick="saveConfiguration()">💾 Save & Apply</button>
                <button class="btn secondary" onclick="loadConfigurationForm()">🔄 Reload Form</button>
                <button class="btn secondary" onclick="testConfiguration()">🧪 Test Settings</button>
                <button class="btn warning" onclick="updateToNewDefaults()">🔄 Update Portal URL</button>
                <button class="btn warning" onclick="updateToNewServerURLs()">🌐 Update Server URLs</button>
                <button class="btn warning" onclick="fixMapServerToFeatureServer()">🔧 Fix MapServer → FeatureServer</button>
                <button class="btn warning" onclick="updateToNewFieldStructure()">🏢 Update to Building Fields</button>
                <button class="btn warning" onclick="diagnoseLayerIssues()">🔍 Diagnose Layer Issues</button>
                <button class="btn danger" onclick="clearCachedConfigAndReload()">🗑️ Clear Cache & Fix CORS</button>
                <button class="btn danger" onclick="resetToDefaults()">⚠️ Reset to Defaults</button>
            </div>
            
            <div class="buttons">
                <a href="index.html" class="btn secondary">🗺️ Open Map Application</a>
                <a href="index.html?debug=true" class="btn secondary">🔍 Open with Debug Mode</a>
            </div>
        </div>
    </div>

    <script>
        // Simplified configuration app without ArcGIS module dependencies

        // Security utilities for credential encryption (shared with index.html)
        const SecurityUtils = {
            // Encrypt sensitive data using Web Crypto API
            async encryptData(data, password) {
                try {
                    const encoder = new TextEncoder();
                    const salt = crypto.getRandomValues(new Uint8Array(16));
                    const iv = crypto.getRandomValues(new Uint8Array(12));
                    
                    const key = await crypto.subtle.importKey(
                        'raw',
                        encoder.encode(password),
                        { name: 'PBKDF2' },
                        false,
                        ['deriveKey']
                    );
                    
                    const derivedKey = await crypto.subtle.deriveKey(
                        {
                            name: 'PBKDF2',
                            salt: salt,
                            iterations: 100000,
                            hash: 'SHA-256'
                        },
                        key,
                        { name: 'AES-GCM', length: 256 },
                        false,
                        ['encrypt']
                    );
                    
                    const encrypted = await crypto.subtle.encrypt(
                        { name: 'AES-GCM', iv: iv },
                        derivedKey,
                        encoder.encode(data)
                    );
                    
                    return {
                        encrypted: Array.from(new Uint8Array(encrypted)),
                        salt: Array.from(salt),
                        iv: Array.from(iv)
                    };
                } catch (error) {
                    console.error('Encryption error:', error);
                    throw new Error('Failed to encrypt data');
                }
            },
            
            // Decrypt sensitive data
            async decryptData(encryptedData, password) {
                try {
                    const encoder = new TextEncoder();
                    const decoder = new TextDecoder();
                    
                    const key = await crypto.subtle.importKey(
                        'raw',
                        encoder.encode(password),
                        { name: 'PBKDF2' },
                        false,
                        ['deriveKey']
                    );
                    
                    const derivedKey = await crypto.subtle.deriveKey(
                        {
                            name: 'PBKDF2',
                            salt: new Uint8Array(encryptedData.salt),
                            iterations: 100000,
                            hash: 'SHA-256'
                        },
                        key,
                        { name: 'AES-GCM', length: 256 },
                        false,
                        ['decrypt']
                    );
                    
                    const decrypted = await crypto.subtle.decrypt(
                        { name: 'AES-GCM', iv: new Uint8Array(encryptedData.iv) },
                        derivedKey,
                        new Uint8Array(encryptedData.encrypted)
                    );
                    
                    return decoder.decode(decrypted);
                } catch (error) {
                    console.error('Decryption error:', error);
                    throw new Error('Failed to decrypt data');
                }
            },
            
            // Input validation and sanitization
            validateAndSanitize: {
                url(input) {
                    if (!input || typeof input !== 'string') return null;
                    try {
                        const url = new URL(input.trim());
                        return ['http:', 'https:'].includes(url.protocol) ? url.href : null;
                    } catch {
                        return null;
                    }
                },
                
                text(input, maxLength = 1000) {
                    if (!input || typeof input !== 'string') return '';
                    return input.trim().substring(0, maxLength).replace(/[<>\"'&]/g, '');
                },
                
                number(input, min = -Infinity, max = Infinity) {
                    const num = parseFloat(input);
                    return isNaN(num) ? null : Math.max(min, Math.min(max, num));
                },
                
                coordinates(input) {
                    try {
                        const coords = Array.isArray(input) ? input : JSON.parse(input);
                        if (!Array.isArray(coords) || coords.length !== 2) return null;
                        const [lng, lat] = coords.map(c => parseFloat(c));
                        if (isNaN(lng) || isNaN(lat)) return null;
                        return [Math.max(-180, Math.min(180, lng)), Math.max(-90, Math.min(90, lat))];
                    } catch {
                        return null;
                    }
                }
            }
        };

        // Configuration Management System (shared with index.html)
        let AppConfig = {
            // Default configuration
            default: {
                app: {
                    title: "ERDB Control Map",
                    description: "This system is use to upload Items to the Portal.",
                    logoHeading: "ERDB Control Map",
                    signOutDescription: "This system is use to upload Items to the Portal. To sign out, click on the logged in user button.",
                    signInDescription: "Use OAuth to log in to an ArcGIS Organization to view your items."
                },
                map: {
                    center: [122, 12],
                    zoom: 6,
                    basemap: "topo",
                    alternateBasemap: "satellite"
                },
                services: {
                    portalUrl: "https://controlmap.denr.gov.ph/arcgis",
                    featureLayerUrl: "https://controlmap.denr.gov.ph/server/rest/services/Hosted/survey123_698ecf476c9640b6a70fc48da500e50a_results/FeatureServer"
                },
                authentication: {
                    appId: "uvfX7FFxhzf31s1Z"
                },
                layout: {
                    mapHeight: "70%",
                    tableHeight: "30%"
                },
                popupTemplate: {
                    title: "{name_building}",
                    fieldInfos: [
                        // Administrative Information
                        { fieldName: "office_level_1", label: "Office Level 1" },
                        { fieldName: "office_level_2", label: "Office Level 2" },
                        { fieldName: "region", label: "Region" },
                        { fieldName: "bureau", label: "Bureau" },
                        { fieldName: "penro", label: "PENRO" },
                        { fieldName: "cenro", label: "CENRO" },
                        
                        // Building Information
                        { fieldName: "name_building", label: "Building Name" },
                        { fieldName: "facility_code", label: "Facility Code" },
                        { fieldName: "facility_id", label: "Facility ID" },
                        { fieldName: "building_purpose", label: "Building Purpose" },
                        { fieldName: "building_purpose_others", label: "Other Purpose" },
                        { fieldName: "year_constructed", label: "Year Constructed" },
                        { fieldName: "floor_area", label: "Floor Area (sq.m)", format: { digitSeparator: true, places: 2 } },
                        { fieldName: "number_floor", label: "Number of Floors" },
                        { fieldName: "max_occupants", label: "Max Occupants", format: { digitSeparator: true } },
                        { fieldName: "buildingvalue", label: "Building Value", format: { digitSeparator: true, places: 2 } },
                        
                        // Land Information
                        { fieldName: "street_address", label: "Street Address" },
                        { fieldName: "land_area", label: "Land Area (sq.m)", format: { digitSeparator: true, places: 2 } },
                        { fieldName: "registered_owner", label: "Registered Owner" },
                        { fieldName: "ownership_type", label: "Ownership Type" },
                        { fieldName: "year_acquired", label: "Year Acquired" },
                        
                        // Data Entry Information
                        { fieldName: "name_encoder", label: "Encoder" },
                        { fieldName: "email", label: "Email" },
                        { fieldName: "remarks", label: "Remarks" }
                    ]
                },
                tableTemplate: {
                    columnTemplates: [
                        // Key Building Information (most important columns first)
                        { type: "field", fieldName: "name_building", label: "Building Name" },
                        { type: "field", fieldName: "facility_code", label: "Facility Code" },
                        { type: "field", fieldName: "building_purpose", label: "Building Purpose" },
                        { type: "field", fieldName: "region", label: "Region" },
                        { type: "field", fieldName: "bureau", label: "Bureau" },
                        { type: "field", fieldName: "office_level_1", label: "Office Level 1" },
                        { type: "field", fieldName: "office_level_2", label: "Office Level 2" },
                        
                        // Physical Information
                        { type: "field", fieldName: "street_address", label: "Street Address" },
                        { type: "field", fieldName: "floor_area", label: "Floor Area (sq.m)" },
                        { type: "field", fieldName: "land_area", label: "Land Area (sq.m)" },
                        { type: "field", fieldName: "number_floor", label: "Floors" },
                        { type: "field", fieldName: "max_occupants", label: "Max Occupants" },
                        { type: "field", fieldName: "year_constructed", label: "Year Built" },
                        { type: "field", fieldName: "buildingvalue", label: "Building Value" },
                        
                        // Ownership & Administrative
                        { type: "field", fieldName: "registered_owner", label: "Owner" },
                        { type: "field", fieldName: "ownership_type", label: "Ownership Type" },
                        { type: "field", fieldName: "year_acquired", label: "Year Acquired" },
                        { type: "field", fieldName: "penro", label: "PENRO" },
                        { type: "field", fieldName: "cenro", label: "CENRO" },
                        
                        // Additional Information
                        { type: "field", fieldName: "facility_id", label: "Facility ID" },
                        { type: "field", fieldName: "building_purpose_others", label: "Other Purpose" },
                        { type: "field", fieldName: "name_encoder", label: "Encoder" },
                        { type: "field", fieldName: "email", label: "Email" },
                        { type: "field", fieldName: "remarks", label: "Remarks" }
                    ]
                },
                users: [
                    { dataUser: "ERDB_THWRDEC", displayName: "THWRDEC" },
                    { dataUser: "ERDB_CRERDEC", displayName: "CRERDEC" },
                    { dataUser: "ERDB_WWRRDEC", displayName: "WWRRDEC" },
                    { dataUser: "ERDB_FWRDEC", displayName: "FWRDEC" },
                    { dataUser: "ERDB_UBRDEC", displayName: "UBRDEC" },
                    { dataUser: "ERDB_ARDEC", displayName: "ARDEC" },
                    { dataUser: "ERDB_FERD", displayName: "FERD" },
                    { dataUser: "ERDB_CZFERD", displayName: "CZFERD" },
                    { dataUser: "ERDB_UERD", displayName: "UERD" }
                ],
                featureTable: {
                    visibleElements: {
                        menuItems: {
                            clearSelection: true,
                            refreshData: true,
                            toggleColumns: true,
                            selectedRecordsShowAllToggle: true,
                            selectedRecordsShowSelectedToggle: true,
                            zoomToSelection: true
                        }
                    }
                },
                upload: {
                    allowedFileTypes: [".zip"],
                    maxRecordCount: 1000,
                    enforceInputFileSizeLimit: true,
                    enforceOutputJsonSizeLimit: true,
                    generalize: false,
                    maxAllowableOffset: 10,
                    reducePrecision: false,
                    numberOfDigitsAfterDecimal: 0
                }
            },

            // Load configuration from localStorage or use defaults
            load() {
                try {
                    const stored = localStorage.getItem('erdb-config');
                    if (stored) {
                        const config = JSON.parse(stored);
                        console.log('Configuration loaded from localStorage');
                        return { ...this.default, ...config };
                    }
                } catch (error) {
                    console.warn('Error loading stored configuration:', error.message);
                }
                console.log('Using default configuration');
                return { ...this.default };
            },

            // Save configuration to localStorage with security enhancements
            async save(config) {
                try {
                    // Validate configuration before saving
                    const validatedConfig = this.validateConfiguration(config);
                    
                    // Encrypt sensitive authentication data
                    if (validatedConfig.authentication && validatedConfig.authentication.password) {
                        const deviceId = await this.getDeviceId();
                        const encryptedPassword = await SecurityUtils.encryptData(
                            validatedConfig.authentication.password, 
                            deviceId
                        );
                        validatedConfig.authentication.password = encryptedPassword;
                        validatedConfig.authentication.encrypted = true;
                    }
                    
                    localStorage.setItem('erdb-config', JSON.stringify(validatedConfig));
                    console.log('Configuration saved securely to localStorage');
                    return true;
                } catch (error) {
                    console.error('Error saving configuration:', error.message);
                    return false;
                }
            },

            // Validate configuration object
            validateConfiguration(config) {
                const validated = { ...config };
                
                // Validate app settings
                if (validated.app) {
                    validated.app.title = SecurityUtils.validateAndSanitize.text(validated.app.title, 100);
                    validated.app.description = SecurityUtils.validateAndSanitize.text(validated.app.description, 500);
                }
                
                // Validate map settings
                if (validated.map) {
                    validated.map.center = SecurityUtils.validateAndSanitize.coordinates(validated.map.center) || [122, 12];
                    validated.map.zoom = SecurityUtils.validateAndSanitize.number(validated.map.zoom, 1, 20) || 6;
                }
                
                // Validate service URLs
                if (validated.services) {
                    validated.services.portalUrl = SecurityUtils.validateAndSanitize.url(validated.services.portalUrl);
                    validated.services.featureLayerUrl = SecurityUtils.validateAndSanitize.url(validated.services.featureLayerUrl);
                }
                
                // Validate authentication settings
                if (validated.authentication) {
                    validated.authentication.appId = SecurityUtils.validateAndSanitize.text(validated.authentication.appId, 100);
                    validated.authentication.username = SecurityUtils.validateAndSanitize.text(validated.authentication.username, 100);
                    // Password will be encrypted separately
                }
                
                return validated;
            },

            // Get device-specific identifier for encryption
            async getDeviceId() {
                try {
                    // Create a device-specific identifier using available browser APIs
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    ctx.textBaseline = 'top';
                    ctx.font = '14px Arial';
                    ctx.fillText('Device fingerprint', 2, 2);
                    
                    const fingerprint = canvas.toDataURL() + 
                                     navigator.userAgent + 
                                     navigator.language + 
                                     screen.width + 'x' + screen.height;
                    
                    // Hash the fingerprint for consistency
                    const encoder = new TextEncoder();
                    const data = encoder.encode(fingerprint);
                    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
                    const hashArray = Array.from(new Uint8Array(hashBuffer));
                    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
                } catch (error) {
                    console.warn('Could not generate device ID:', error);
                    return 'default-device-id';
                }
            },

            // Reset to defaults
            reset() {
                try {
                    localStorage.removeItem('erdb-config');
                    console.log('Configuration reset to defaults');
                    return true;
                } catch (error) {
                    console.error('Error resetting configuration:', error.message);
                    return false;
                }
            },

            // Auto-discover fields from feature layer URL
            async discoverFields(layerUrl, authToken = null) {
                try {
                    console.log('Discovering fields from:', layerUrl);
                    
                    // Construct the REST endpoint URL for layer info
                    let serviceUrl = layerUrl;
                    if (!serviceUrl.endsWith('/')) {
                        serviceUrl += '/';
                    }
                    
                    // Add authentication token if provided
                    const urlParams = new URLSearchParams();
                    urlParams.append('f', 'json');
                    
                    if (authToken) {
                        urlParams.append('token', authToken);
                        console.log('Using authentication token for field discovery');
                    }
                    
                    serviceUrl += '?' + urlParams.toString();

                    const fetchOptions = {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                    };

                    const response = await fetch(serviceUrl, fetchOptions);
                    if (!response.ok) {
                        // Check if it's an authentication error
                        if (response.status === 401 || response.status === 403) {
                            throw new Error('Authentication required. Please check your credentials or ensure the service is publicly accessible.');
                        }
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const layerInfo = await response.json();
                    
                    if (layerInfo.error) {
                        throw new Error(layerInfo.error.message || 'Service returned an error');
                    }

                    if (!layerInfo.fields || !Array.isArray(layerInfo.fields)) {
                        throw new Error('No field information found in the layer response');
                    }

                    console.log(`Found ${layerInfo.fields.length} fields in layer:`, layerInfo.name || 'Unnamed Layer');

                    // Filter out system fields and create field configurations
                    const systemFields = ['OBJECTID', 'SHAPE', 'SHAPE_Length', 'SHAPE_Area', 'GlobalID', 'created_user', 'created_date', 'last_edited_user', 'last_edited_date'];
                    const userFields = layerInfo.fields.filter(field => 
                        !systemFields.includes(field.name) && 
                        !field.name.startsWith('SHAPE') &&
                        field.type !== 'esriFieldTypeGeometry' &&
                        field.type !== 'esriFieldTypeOID'
                    );

                    // Create table column templates
                    const tableColumns = userFields.map(field => ({
                        type: "field",
                        fieldName: field.name,
                        label: field.alias || field.name,
                        direction: "asc"
                    }));

                    // Create popup field infos
                    const popupFields = userFields.map(field => ({
                        fieldName: field.name,
                        label: field.alias || field.name,
                        visible: true
                    }));

                    // Use the first non-system field as popup title if available
                    const titleField = layerInfo.displayField || 
                                     (popupFields.length > 0 ? popupFields[0].fieldName : 'OBJECTID');

                    const discoveredConfig = {
                        tableTemplate: {
                            columnTemplates: tableColumns
                        },
                        popupTemplate: {
                            title: `{${titleField}}`,
                            fieldInfos: popupFields
                        },
                        layerInfo: {
                            name: layerInfo.name || 'Feature Layer',
                            description: layerInfo.description || '',
                            geometryType: layerInfo.geometryType || '',
                            fields: layerInfo.fields,
                            capabilities: layerInfo.capabilities || ''
                        }
                    };

                    console.log('Field discovery successful:', discoveredConfig);
                    return discoveredConfig;

                } catch (error) {
                    console.error('Error discovering fields:', error);
                    throw error;
                }
            },

            // Generate authentication token using username/password
            async generateToken(portalUrl, username, password) {
                try {
                    const tokenUrl = `${portalUrl}/sharing/rest/generateToken`;
                    
                    const params = new URLSearchParams();
                    params.append('username', username);
                    params.append('password', password);
                    params.append('referer', window.location.origin);
                    params.append('f', 'json');
                    
                    const response = await fetch(tokenUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        body: params
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const result = await response.json();
                    
                    if (result.error) {
                        throw new Error(result.error.message || 'Authentication failed');
                    }
                    
                    return result.token;
                } catch (error) {
                    console.error('Token generation error:', error);
                    throw error;
                }
            },

            // Get authentication token based on current configuration
            async getAuthToken() {
                const config = this.load();
                const auth = config.authentication || {};
                
                if (!auth.requireAuth) {
                    return null;
                }
                
                try {
                    switch (auth.authMode) {
                        case 'credentials':
                            if (auth.username && auth.password) {
                                return await this.generateToken(
                                    config.services.portalUrl, 
                                    auth.username, 
                                    auth.password
                                );
                            }
                            break;
                            
                        case 'token':
                            if (auth.token) {
                                // Check if token is expired
                                if (auth.tokenExpiration) {
                                    const expiration = new Date(auth.tokenExpiration);
                                    if (expiration <= new Date()) {
                                        throw new Error('Token has expired');
                                    }
                                }
                                return auth.token;
                            }
                            break;
                            
                        case 'oauth':
                        default:
                            // OAuth tokens are handled by the main application
                            return null;
                    }
                } catch (error) {
                    console.error('Error getting auth token:', error);
                    throw error;
                }
                
                return null;
            },

            // Apply URL parameter overrides to configuration
            applyUrlOverrides(config) {
                const urlParams = new URLSearchParams(window.location.search);
                const overrideConfig = { ...config };
                
                // Map URL parameters to configuration paths
                const paramMapping = {
                    'center': (value) => {
                        try {
                            const coords = JSON.parse(value);
                            if (Array.isArray(coords) && coords.length === 2) {
                                overrideConfig.map.center = coords;
                            }
                        } catch (e) {
                            console.warn('Invalid center parameter:', value);
                        }
                    },
                    'zoom': (value) => {
                        const zoom = parseInt(value);
                        if (!isNaN(zoom) && zoom >= 1 && zoom <= 20) {
                            overrideConfig.map.zoom = zoom;
                        }
                    },
                    'basemap': (value) => {
                        overrideConfig.map.basemap = value;
                    }
                };
                
                // Apply overrides
                for (const [param, handler] of Object.entries(paramMapping)) {
                    if (urlParams.has(param)) {
                        handler(urlParams.get(param));
                    }
                }
                
                return overrideConfig;
            },

            // Update configuration with discovered fields
            async updateWithDiscoveredFields(layerUrl) {
                try {
                    // Get authentication token if required
                    const authToken = await this.getAuthToken();
                    
                    const discoveredConfig = await this.discoverFields(layerUrl, authToken);
                    const currentConfig = this.load();
                    
                    // Merge discovered configuration with current configuration
                    const updatedConfig = {
                        ...currentConfig,
                        tableTemplate: discoveredConfig.tableTemplate,
                        popupTemplate: discoveredConfig.popupTemplate,
                        layerInfo: discoveredConfig.layerInfo
                    };

                    // Save the updated configuration
                    this.save(updatedConfig);
                    console.log('Configuration updated with discovered fields');
                    
                    return updatedConfig;
                } catch (error) {
                    console.error('Error updating configuration with discovered fields:', error);
                    throw error;
                }
            }
        };

        // Form management functions
        function loadConfigurationForm() {
            const config = AppConfig.load();
            
            // Load values into form
            document.getElementById('cfg-title').value = config.app.title || '';
            document.getElementById('cfg-description').value = config.app.description || '';
            document.getElementById('cfg-center-lng').value = config.map.center[0] || '';
            document.getElementById('cfg-center-lat').value = config.map.center[1] || '';
            document.getElementById('cfg-zoom').value = config.map.zoom || '';
            document.getElementById('cfg-basemap').value = config.map.basemap || '';
            document.getElementById('cfg-alt-basemap').value = config.map.alternateBasemap || '';
            document.getElementById('cfg-portal-url').value = config.services.portalUrl || '';
            document.getElementById('cfg-feature-url').value = config.services.featureLayerUrl || '';
            document.getElementById('cfg-map-height').value = config.layout.mapHeight || '';
            document.getElementById('cfg-table-height').value = config.layout.tableHeight || '';
            
            // Load authentication configuration
            const auth = config.authentication || {};
            document.getElementById('cfg-require-auth').checked = auth.requireAuth || false;
            document.getElementById('cfg-auth-mode').value = auth.authMode || 'oauth';
            document.getElementById('cfg-app-id').value = auth.appId || '';
            document.getElementById('cfg-auto-signin').checked = auth.autoSignIn || false;
            document.getElementById('cfg-username').value = auth.username || '';
            document.getElementById('cfg-password').value = auth.password || '';
            document.getElementById('cfg-token').value = auth.token || '';
            
            if (auth.tokenExpiration) {
                const date = new Date(auth.tokenExpiration);
                document.getElementById('cfg-token-expiration').value = date.toISOString().slice(0, 16);
            }
            
            // Update auth UI visibility
            toggleAuthOptions();
            toggleAuthFields();
            
            updatePreview();
            showStatus('Configuration loaded', 'success');
        }

        function toggleAuthOptions() {
            const requireAuth = document.getElementById('cfg-require-auth').checked;
            const authOptions = document.getElementById('auth-options');
            authOptions.style.display = requireAuth ? 'block' : 'none';
        }

        function toggleAuthFields() {
            const authMode = document.getElementById('cfg-auth-mode').value;
            
            // Hide all auth field groups
            document.getElementById('oauth-fields').style.display = 'none';
            document.getElementById('credentials-fields').style.display = 'none';
            document.getElementById('token-fields').style.display = 'none';
            
            // Show relevant fields based on auth mode
            switch (authMode) {
                case 'oauth':
                    document.getElementById('oauth-fields').style.display = 'block';
                    break;
                case 'credentials':
                    document.getElementById('credentials-fields').style.display = 'block';
                    break;
                case 'token':
                    document.getElementById('token-fields').style.display = 'block';
                    break;
            }
        }

        async function testAuthentication() {
            const testBtn = document.getElementById('test-auth-btn');
            const originalText = testBtn.textContent;
            
            try {
                testBtn.textContent = '🔄 Testing...';
                testBtn.disabled = true;
                
                // Create a temporary config with current form values
                const tempConfig = {
                    services: {
                        portalUrl: document.getElementById('cfg-portal-url').value
                    },
                    authentication: {
                        requireAuth: true,
                        authMode: document.getElementById('cfg-auth-mode').value,
                        appId: document.getElementById('cfg-app-id').value,
                        username: document.getElementById('cfg-username').value,
                        password: document.getElementById('cfg-password').value,
                        token: document.getElementById('cfg-token').value,
                        tokenExpiration: document.getElementById('cfg-token-expiration').value
                    }
                };
                
                // Save current config temporarily
                const originalConfig = AppConfig.load();
                AppConfig.save(tempConfig);
                
                try {
                    // Test authentication
                    const token = await AppConfig.getAuthToken();
                    
                    if (token) {
                        showStatus('✅ Authentication successful! Token generated.', 'success');
                    } else if (tempConfig.authentication.authMode === 'oauth') {
                        showStatus('✅ OAuth configuration appears valid. Authentication will be handled by the main application.', 'success');
                    } else {
                        showStatus('⚠️ No token generated. Please check your credentials.', 'error');
                    }
                } finally {
                    // Restore original config
                    AppConfig.save(originalConfig);
                }
                
            } catch (error) {
                showStatus(`❌ Authentication test failed: ${error.message}`, 'error');
            } finally {
                testBtn.textContent = originalText;
                testBtn.disabled = false;
            }
        }

        function updatePreview() {
            const title = document.getElementById('cfg-title').value || 'ERDB Control Map';
            const lng = document.getElementById('cfg-center-lng').value || '122';
            const lat = document.getElementById('cfg-center-lat').value || '12';
            const zoom = document.getElementById('cfg-zoom').value || '6';
            const basemap = document.getElementById('cfg-basemap').value || 'topo';
            const portal = document.getElementById('cfg-portal-url').value || 'Default Portal';
            
            document.getElementById('preview-title').textContent = title;
            document.getElementById('preview-center').textContent = `[${lng}, ${lat}]`;
            document.getElementById('preview-zoom').textContent = zoom;
            document.getElementById('preview-basemap').textContent = basemap.charAt(0).toUpperCase() + basemap.slice(1);
            document.getElementById('preview-portal').textContent = portal.length > 50 ? portal.substring(0, 50) + '...' : portal;
            
            // Update table columns preview
            const config = AppConfig.load();
            if (config.tableTemplate && config.tableTemplate.columnTemplates) {
                const columnCount = config.tableTemplate.columnTemplates.length;
                const columnNames = config.tableTemplate.columnTemplates.slice(0, 3).map(col => col.label || col.fieldName).join(', ');
                document.getElementById('preview-columns').textContent = 
                    `${columnCount} columns (${columnNames}${columnCount > 3 ? '...' : ''})`;
            }
        }

        // Field Discovery Functions
        async function discoverFields() {
            const layerUrl = document.getElementById('cfg-feature-url').value.trim();
            
            if (!layerUrl) {
                showDiscoveryStatus('Please enter a feature layer URL first.', 'error');
                return;
            }

            // Validate URL format
            try {
                new URL(layerUrl);
            } catch (e) {
                showDiscoveryStatus('Please enter a valid URL.', 'error');
                return;
            }

            const discoverBtn = document.getElementById('discover-btn');
            const originalText = discoverBtn.textContent;
            
            try {
                // Update button state
                discoverBtn.textContent = '🔄 Discovering...';
                discoverBtn.disabled = true;
                
                showDiscoveryStatus('Connecting to feature service...', 'info');

                // Use the same discovery logic from index.html
                const discoveredConfig = await AppConfig.discoverFields(layerUrl);
                
                // Update the current configuration with discovered fields
                const currentConfig = AppConfig.load();
                const updatedConfig = {
                    ...currentConfig,
                    tableTemplate: discoveredConfig.tableTemplate,
                    popupTemplate: discoveredConfig.popupTemplate,
                    layerInfo: discoveredConfig.layerInfo
                };

                AppConfig.save(updatedConfig);
                
                // Display discovered information
                displayDiscoveredInfo(discoveredConfig);
                
                showDiscoveryStatus(
                    `✅ Successfully discovered ${discoveredConfig.tableTemplate.columnTemplates.length} fields from the layer!`, 
                    'success'
                );
                
                // Update preview
                updatePreview();

            } catch (error) {
                console.error('Field discovery error:', error);
                showDiscoveryStatus(`❌ Error: ${error.message}`, 'error');
            } finally {
                // Reset button state
                discoverBtn.textContent = originalText;
                discoverBtn.disabled = false;
            }
        }

        function displayDiscoveredInfo(discoveredConfig) {
            const section = document.getElementById('field-discovery-section');
            const layerInfo = discoveredConfig.layerInfo;
            
            // Update layer information
            document.getElementById('layer-name').textContent = layerInfo.name || 'Unknown';
            document.getElementById('layer-geometry').textContent = layerInfo.geometryType || 'Unknown';
            document.getElementById('field-count').textContent = layerInfo.fields ? layerInfo.fields.length : 0;
            document.getElementById('layer-capabilities').textContent = layerInfo.capabilities || 'Not specified';
            
            // Display discovered fields
            const fieldsList = document.getElementById('discovered-fields-list');
            fieldsList.innerHTML = '';
            
            if (layerInfo.fields && layerInfo.fields.length > 0) {
                const table = document.createElement('table');
                table.style.cssText = 'width: 100%; border-collapse: collapse; font-size: 0.9em;';
                
                // Create header
                const header = table.createTHead();
                const headerRow = header.insertRow();
                ['Field Name', 'Type', 'Alias', 'Length'].forEach(text => {
                    const th = document.createElement('th');
                    th.textContent = text;
                    th.style.cssText = 'border: 1px solid #ddd; padding: 8px; background: #f5f5f5; text-align: left;';
                    headerRow.appendChild(th);
                });
                
                // Create body
                const tbody = table.createTBody();
                layerInfo.fields.forEach(field => {
                    const row = tbody.insertRow();
                    [
                        field.name,
                        field.type.replace('esriFieldType', ''),
                        field.alias || '-',
                        field.length || '-'
                    ].forEach(text => {
                        const td = row.insertCell();
                        td.textContent = text;
                        td.style.cssText = 'border: 1px solid #ddd; padding: 8px;';
                    });
                });
                
                fieldsList.appendChild(table);
            } else {
                fieldsList.innerHTML = '<p>No fields discovered</p>';
            }
            
            // Show the section
            section.style.display = 'block';
        }

        function showDiscoveryStatus(message, type) {
            const statusEl = document.getElementById('discovery-status');
            statusEl.innerHTML = message;
            statusEl.style.display = 'block';
            
            // Style based on type
            switch (type) {
                case 'success':
                    statusEl.style.cssText += 'background: #d4edda; color: #155724; border: 1px solid #c3e6cb;';
                    break;
                case 'error':
                    statusEl.style.cssText += 'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;';
                    break;
                case 'info':
                    statusEl.style.cssText += 'background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb;';
                    break;
            }
            
            // Auto-hide success/info messages after 5 seconds
            if (type !== 'error') {
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 5000);
            }
        }

        async function saveConfiguration() {
            try {
                // Get values from form with validation
                const newConfig = {
                    app: {
                        title: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-title').value),
                        description: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-description').value),
                        logoHeading: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-title').value),
                        signOutDescription: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-description').value) + " To sign out, click on the logged in user button.",
                        signInDescription: "Use OAuth to log in to an ArcGIS Organization to view your items."
                    },
                    map: {
                        center: [
                            SecurityUtils.validateAndSanitize.number(document.getElementById('cfg-center-lng').value, -180, 180) || 122,
                            SecurityUtils.validateAndSanitize.number(document.getElementById('cfg-center-lat').value, -90, 90) || 12
                        ],
                        zoom: SecurityUtils.validateAndSanitize.number(document.getElementById('cfg-zoom').value, 1, 20) || 6,
                        basemap: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-basemap').value, 50),
                        alternateBasemap: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-alt-basemap').value, 50)
                    },
                    services: {
                        portalUrl: SecurityUtils.validateAndSanitize.url(document.getElementById('cfg-portal-url').value),
                        featureLayerUrl: SecurityUtils.validateAndSanitize.url(document.getElementById('cfg-feature-url').value)
                    },
                    authentication: {
                        appId: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-app-id').value),
                        requireAuth: document.getElementById('cfg-require-auth').checked,
                        authMode: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-auth-mode').value, 20),
                        username: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-username').value),
                        password: document.getElementById('cfg-password').value, // Will be encrypted by save method
                        token: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-token').value),
                        tokenExpiration: document.getElementById('cfg-token-expiration').value,
                        autoSignIn: document.getElementById('cfg-auto-signin').checked
                    },
                    layout: {
                        mapHeight: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-map-height').value, 10),
                        tableHeight: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-table-height').value, 10)
                    }
                };

                // Validate URLs
                if (newConfig.services.portalUrl === null) {
                    showStatus('Invalid Portal URL. Please enter a valid HTTPS URL.', 'error');
                    return;
                }
                
                if (newConfig.services.featureLayerUrl === null) {
                    showStatus('Invalid Feature Layer URL. Please enter a valid HTTPS URL.', 'error');
                    return;
                }

                // Merge with defaults to ensure all properties exist
                const fullConfig = { ...AppConfig.default, ...newConfig };
                
                // Save configuration with encryption
                const saveResult = await AppConfig.save(fullConfig);
                if (saveResult) {
                    showStatus('✅ Configuration saved successfully! Changes will apply when you reload the map application.', 'success');
                    updatePreview();
                    
                    // Clear password field for security
                    document.getElementById('cfg-password').value = '';
                } else {
                    showStatus('❌ Error saving configuration. Please try again.', 'error');
                }
            } catch (error) {
                showStatus('❌ Error saving configuration: ' + error.message, 'error');
                console.error('Configuration save error:', error);
            }
        }

        function resetToDefaults() {
            if (confirm('Are you sure you want to reset to default configuration? This will clear all custom settings and use the latest default values.')) {
                if (AppConfig.reset()) {
                    showStatus('✅ Configuration reset to defaults! New portal URL updated.', 'success');
                    loadConfigurationForm();
                } else {
                    showStatus('❌ Error resetting configuration. Please try again.', 'error');
                }
            }
        }

        function updateToNewDefaults() {
            if (confirm('Update your configuration to use the new default portal URL while keeping your other settings?')) {
                try {
                    const currentConfig = AppConfig.load();
                    const updatedConfig = {
                        ...currentConfig,
                        services: {
                            ...currentConfig.services,
                            portalUrl: AppConfig.default.services.portalUrl
                        }
                    };
                    
                    if (AppConfig.save(updatedConfig)) {
                        showStatus('✅ Configuration updated with new portal URL!', 'success');
                        loadConfigurationForm();
                    } else {
                        showStatus('❌ Error updating configuration. Please try again.', 'error');
                    }
                } catch (error) {
                    showStatus('❌ Error updating configuration: ' + error.message, 'error');
                }
            }
        }

        function updateToNewServerURLs() {
            if (confirm('Update your configuration to use the new server URLs (remove -erdb from domains) while keeping your other settings?')) {
                try {
                    const currentConfig = AppConfig.load();
                    const updatedConfig = {
                        ...currentConfig,
                        services: {
                            portalUrl: AppConfig.default.services.portalUrl,
                            featureLayerUrl: AppConfig.default.services.featureLayerUrl
                        }
                    };
                    
                    if (AppConfig.save(updatedConfig)) {
                        showStatus('✅ Configuration updated with new server URLs! Old: controlmap-erdb.denr.gov.ph → New: controlmap.denr.gov.ph', 'success');
                        loadConfigurationForm();
                    } else {
                        showStatus('❌ Error updating configuration. Please try again.', 'error');
                    }
                } catch (error) {
                    showStatus('❌ Error updating configuration: ' + error.message, 'error');
                }
            }
        }

        function fixMapServerToFeatureServer() {
            if (confirm('Fix MapServer URLs to FeatureServer URLs? This will change any MapServer references to FeatureServer for proper editing functionality.')) {
                try {
                    const currentConfig = AppConfig.load();
                    
                    // Fix the feature layer URL
                    let updatedFeatureLayerUrl = currentConfig.services.featureLayerUrl;
                    if (updatedFeatureLayerUrl && updatedFeatureLayerUrl.includes('MapServer')) {
                        updatedFeatureLayerUrl = updatedFeatureLayerUrl.replace('MapServer', 'FeatureServer');
                    }
                    
                    const updatedConfig = {
                        ...currentConfig,
                        services: {
                            ...currentConfig.services,
                            featureLayerUrl: updatedFeatureLayerUrl
                        }
                    };
                    
                    if (AppConfig.save(updatedConfig)) {
                        showStatus('✅ Configuration updated! MapServer → FeatureServer for editing support', 'success');
                        loadConfigurationForm();
                    } else {
                        showStatus('❌ Error updating configuration. Please try again.', 'error');
                    }
                } catch (error) {
                    showStatus('❌ Error updating configuration: ' + error.message, 'error');
                }
            }
        }

        function clearCachedConfigAndReload() {
            if (confirm('Clear all cached configuration and reload with latest defaults? This will fix CORS errors caused by old portal URLs.')) {
                try {
                    // Clear all configuration from localStorage (try both key formats)
                    localStorage.removeItem('erdb-config');
                    localStorage.removeItem('erdb_config');
                    localStorage.removeItem('erdb_config_encrypted');
                    
                    showStatus('✅ Cached configuration cleared! Reloading with new defaults...', 'success');
                    
                    // Reload after a short delay to show the success message
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } catch (error) {
                    showStatus('❌ Error clearing configuration: ' + error.message, 'error');
                }
            }
        }

        function updateToNewFieldStructure() {
            if (confirm('Update your configuration to use the new building/facility field structure? This will replace popup and table templates with the new field mappings.')) {
                try {
                    const currentConfig = AppConfig.load();
                    const updatedConfig = {
                        ...currentConfig,
                        popupTemplate: AppConfig.default.popupTemplate,
                        tableTemplate: AppConfig.default.tableTemplate
                    };
                    
                    if (AppConfig.save(updatedConfig)) {
                        showStatus('✅ Configuration updated with new field structure! Popup and table now show building/facility fields.', 'success');
                        loadConfigurationForm();
                    } else {
                        showStatus('❌ Error updating configuration. Please try again.', 'error');
                    }
                } catch (error) {
                    showStatus('❌ Error updating configuration: ' + error.message, 'error');
                }
            }
        }

        async function diagnoseLayerIssues() {
            showStatus('🔍 Running layer diagnostics...', 'info');
            
            try {
                const config = AppConfig.load();
                const featureLayerUrl = config.services.featureLayerUrl;
                
                console.log('Diagnosing layer issues for URL:', featureLayerUrl);
                
                let diagnosticResults = [];
                
                // Check 1: URL format validation
                try {
                    const url = new URL(featureLayerUrl);
                    diagnosticResults.push(`✅ URL format is valid: ${url.protocol}://${url.host}`);
                } catch (e) {
                    diagnosticResults.push(`❌ Invalid URL format: ${featureLayerUrl}`);
                    showStatus('❌ Invalid feature layer URL format detected!', 'error');
                    return;
                }
                
                // Check 2: MapServer vs FeatureServer
                if (featureLayerUrl.includes('MapServer')) {
                    diagnosticResults.push(`⚠️ MapServer URL detected - should be FeatureServer for editing`);
                    diagnosticResults.push(`📝 Suggested fix: ${featureLayerUrl.replace('MapServer', 'FeatureServer')}`);
                } else if (featureLayerUrl.includes('FeatureServer')) {
                    diagnosticResults.push(`✅ FeatureServer URL format is correct`);
                } else {
                    diagnosticResults.push(`⚠️ Unknown service type in URL`);
                }
                
                // Check 3: Service connectivity
                try {
                    showStatus('🔍 Testing service connectivity...', 'info');
                    
                    const testUrl = featureLayerUrl + '?f=json';
                    const response = await fetch(testUrl, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (response.ok) {
                        const serviceInfo = await response.json();
                        if (serviceInfo.error) {
                            diagnosticResults.push(`❌ Service error: ${serviceInfo.error.message}`);
                        } else {
                            diagnosticResults.push(`✅ Service is accessible`);
                            if (serviceInfo.name) {
                                diagnosticResults.push(`📋 Service name: ${serviceInfo.name}`);
                            }
                            if (serviceInfo.capabilities) {
                                diagnosticResults.push(`🔧 Capabilities: ${serviceInfo.capabilities}`);
                            }
                        }
                    } else {
                        diagnosticResults.push(`❌ HTTP ${response.status}: ${response.statusText}`);
                        
                        if (response.status === 404) {
                            diagnosticResults.push(`💡 Service not found - check if the service exists at this URL`);
                        } else if (response.status === 403) {
                            diagnosticResults.push(`💡 Access denied - may require authentication`);
                        }
                    }
                } catch (fetchError) {
                    diagnosticResults.push(`❌ Connection failed: ${fetchError.message}`);
                    
                    if (fetchError.message.includes('CORS')) {
                        diagnosticResults.push(`💡 CORS issue detected - try clearing cache or updating portal URL`);
                    } else if (fetchError.message.includes('network')) {
                        diagnosticResults.push(`💡 Network issue - check internet connection and service URL`);
                    }
                }
                
                // Check 4: Configuration consistency
                const portalUrl = config.services.portalUrl;
                const layerDomain = new URL(featureLayerUrl).origin;
                const portalDomain = new URL(portalUrl).origin;
                
                if (layerDomain === portalDomain) {
                    diagnosticResults.push(`✅ Portal and layer URLs use the same domain`);
                } else {
                    diagnosticResults.push(`⚠️ Portal (${portalDomain}) and layer (${layerDomain}) use different domains`);
                }
                
                // Display results
                const resultHtml = `
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 5px solid #007bff;">
                        <h4>🔍 Layer Diagnostic Results</h4>
                        <div style="font-family: monospace; font-size: 0.9em;">
                            ${diagnosticResults.map(result => `<div style="margin: 5px 0;">${result}</div>`).join('')}
                        </div>
                        <hr style="margin: 15px 0;">
                        <strong>Current Configuration:</strong><br>
                        <div style="font-family: monospace; font-size: 0.85em; background: #e9ecef; padding: 10px; border-radius: 4px; margin-top: 5px;">
                            Portal: ${portalUrl}<br>
                            Layer: ${featureLayerUrl}
                        </div>
                    </div>
                `;
                
                // Show results in a temporary section
                let resultsSection = document.getElementById('diagnostic-results');
                if (!resultsSection) {
                    resultsSection = document.createElement('div');
                    resultsSection.id = 'diagnostic-results';
                    document.querySelector('.content').appendChild(resultsSection);
                }
                resultsSection.innerHTML = resultHtml;
                
                showStatus('✅ Diagnostic complete! See results above.', 'success');
                
            } catch (error) {
                console.error('Diagnostic error:', error);
                showStatus(`❌ Diagnostic failed: ${error.message}`, 'error');
            }
        }

        function testConfiguration() {
            const testUrl = 'index.html?' + new URLSearchParams({
                basemap: document.getElementById('cfg-basemap').value,
                center: `[${document.getElementById('cfg-center-lng').value},${document.getElementById('cfg-center-lat').value}]`,
                zoom: document.getElementById('cfg-zoom').value,
                debug: 'true'
            }).toString();
            
            window.open(testUrl, '_blank');
            showStatus('Opening test window with current settings...', 'success');
        }

        function showStatus(message, type) {
            const statusEl = document.getElementById('status-message');
            statusEl.textContent = message;
            statusEl.className = `status-message status-${type}`;
            statusEl.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 5000);
        }

        // Configuration access control - simplified approach
        const AUTHORIZED_CONFIG_USERS = ['afolvida'];
        
        function checkConfigurationAccess() {
            try {
                // Check if user is authenticated via localStorage (from main app)
                const authenticatedUser = localStorage.getItem('erdb_authenticated_user');
                const authTimestamp = localStorage.getItem('erdb_auth_timestamp');
                
                console.log(`Checking stored authentication: user=${authenticatedUser}, timestamp=${authTimestamp}`);
                
                if (!authenticatedUser || !authTimestamp) {
                    console.log('No stored authentication found');
                    showSignInRequiredMessage();
                    return false;
                }
                
                // Check if authentication is recent (within 24 hours)
                const currentTime = Date.now();
                const storedTime = parseInt(authTimestamp);
                const timeDifference = currentTime - storedTime;
                const twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
                
                if (timeDifference > twentyFourHours) {
                    console.log('Stored authentication is too old, clearing...');
                    localStorage.removeItem('erdb_authenticated_user');
                    localStorage.removeItem('erdb_auth_timestamp');
                    showSignInRequiredMessage();
                    return false;
                }
                
                // Check if user is authorized
                if (AUTHORIZED_CONFIG_USERS.includes(authenticatedUser.toLowerCase())) {
                    console.log(`Configuration access granted for user: ${authenticatedUser}`);
                    return true;
                } else {
                    console.log(`Access denied for user: ${authenticatedUser}`);
                    showAccessDeniedMessage(authenticatedUser);
                    return false;
                }
                
            } catch (error) {
                console.warn('Authentication check failed:', error.message || error);
                showSignInRequiredMessage();
                return false;
            }
        }
        
        function showAccessDeniedMessage(username) {
            document.body.innerHTML = `
                <div style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    font-family: Arial, sans-serif;
                    color: white;
                    text-align: center;
                ">
                    <div style="
                        background: rgba(255,255,255,0.1);
                        padding: 40px;
                        border-radius: 12px;
                        backdrop-filter: blur(10px);
                        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                        max-width: 500px;
                    ">
                        <h1 style="margin: 0 0 20px 0; color: #ff6b6b;">🚫 Access Denied</h1>
                        <p style="margin: 0 0 20px 0; font-size: 18px;">
                            Sorry <strong>${username}</strong>, you do not have permission to access the configuration interface.
                        </p>
                        <p style="margin: 0 0 30px 0; opacity: 0.8;">
                            Only authorized administrators can modify system settings.
                        </p>
                        <button onclick="window.close()" style="
                            background: #4CAF50;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 16px;
                            margin-right: 10px;
                        ">Close Window</button>
                        <button onclick="window.location.href='index.html'" style="
                            background: #2196F3;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 16px;
                        ">Return to Map</button>
                    </div>
                </div>
            `;
        }
        
        function showSignInRequiredMessage() {
            document.body.innerHTML = `
                <div style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    font-family: Arial, sans-serif;
                    color: white;
                    text-align: center;
                ">
                    <div style="
                        background: rgba(255,255,255,0.1);
                        padding: 40px;
                        border-radius: 12px;
                        backdrop-filter: blur(10px);
                        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                        max-width: 500px;
                    ">
                        <h1 style="margin: 0 0 20px 0; color: #ffa726;">🔐 Authentication Required</h1>
                        <p style="margin: 0 0 15px 0; font-size: 18px;">
                            Please sign in through the main application first.
                        </p>
                        <p style="margin: 0 0 20px 0; font-size: 14px; opacity: 0.9;">
                            <strong>To access configuration:</strong><br>
                            1. Sign in on the main map application<br>
                            2. Click the ⚙️ gear icon to open configuration<br>
                            3. Configuration will inherit your authentication
                        </p>
                        <p style="margin: 0 0 30px 0; opacity: 0.8;">
                            Configuration access is restricted to authorized users only.
                        </p>
                        <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                            <button onclick="window.location.reload()" style="
                                background: #ff9800;
                                color: white;
                                border: none;
                                padding: 12px 20px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                            ">🔄 Try Again</button>
                            <button onclick="window.location.href='index.html'" style="
                                background: #4CAF50;
                                color: white;
                                border: none;
                                padding: 12px 20px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                            ">🗺️ Go to Map App</button>
                        </div>
                    </div>
                </div>
            `;
        }

        // Initialize configuration app - simplified without ArcGIS modules
        function initializeConfigurationApp() {
            console.log('Initializing configuration app...');
            
            // Check access before loading configuration interface
            const hasAccess = checkConfigurationAccess();
            
            if (hasAccess) {
                // Load the configuration interface
                loadConfigurationForm();
                
                // Update preview when form changes
                const formInputs = document.querySelectorAll('input, select');
                formInputs.forEach(input => {
                    input.addEventListener('input', updatePreview);
                    input.addEventListener('change', updatePreview);
                });
                
                // Add specific event listener for auth checkbox
                document.getElementById('cfg-require-auth').addEventListener('change', toggleAuthOptions);
            }
            // If no access, the appropriate message has already been shown
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Configuration page DOM loaded, initializing...');
            initializeConfigurationApp();
        });
    </script>
</body>
</html>
