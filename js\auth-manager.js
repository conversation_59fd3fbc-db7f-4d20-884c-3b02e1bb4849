/**
 * Enhanced Authentication Manager
 * Fixes session persistence issues and improves OAuth flow
 */

const AuthManager = {
    initialized: false,
    currentUser: null,
    portalUrl: null,
    esriId: null,
    config: null,
    
    /**
     * Initialize the authentication manager
     * @param {Object} esriIdentityManager - ArcGIS Identity Manager
     * @param {Object} config - Application configuration
     * @param {Object} esriClasses - Required ArcGIS classes {OAuthInfo, Portal}
     */
    init(esriIdentityManager, config, esriClasses = {}) {
        this.esriId = esriIdentityManager;
        this.config = config;
        this.portalUrl = config.services.portalUrl;
        this.OAuthInfo = esriClasses.OAuthInfo;
        this.Portal = esriClasses.Portal;
        
        console.log('🔐 Enhanced AuthManager initializing...');
        
        // Check if required classes are available
        if (!this.OAuthInfo || !this.Portal) {
            console.warn('⚠️ Required ArcGIS classes not available, falling back to legacy auth');
            return false;
        }
        
        // Configure OAuth with improved settings
        this.setupOAuth();
        
        // Configure session persistence
        this.setupSessionPersistence();
        
        // Attempt session restoration
        this.restoreSession();
        
        this.initialized = true;
        console.log('✅ Enhanced AuthManager ready');
        return true;
    },

    /**
     * Setup OAuth configuration with improved persistence
     */
    setupOAuth() {
        // Use base portal URL for OAuth (without /home)
        const cleanPortalUrl = this.portalUrl.replace('/home', '');
        
        console.log('🔧 Configuring OAuth with portal:', cleanPortalUrl);
        
        const oauthInfo = new this.OAuthInfo({
            appId: this.config.authentication.appId,
            portalUrl: cleanPortalUrl, // Use clean URL consistently
            popup: false, // Use redirect flow for seamless experience
            flowType: "auto", // Let ArcGIS choose the best flow
            expiration: 20160, // 14 days
            locale: "en",
            preserveUrlHash: true
        });

        this.esriId.registerOAuthInfos([oauthInfo]);
        console.log('✅ OAuth configured with redirect flow');
    },

    /**
     * Setup session persistence with enhanced settings
     */
    setupSessionPersistence() {
        // Enhanced session persistence settings
        this.esriId.useSignInPage = false;
        this.esriId.tokenDuration = 20160; // 14 days in minutes
        
        // Store credentials in localStorage for persistence
        this.esriId.on('credential-create', (evt) => {
            console.log('💾 New credential created, storing for persistence');
            this.storeCredentials();
        });

        // Clear stored credentials on destroy
        this.esriId.on('credentials-destroy', () => {
            console.log('🗑️ Credentials destroyed, clearing storage');
            this.clearStoredCredentials();
        });

        console.log('✅ Session persistence configured');
    },

    /**
     * Attempt to restore existing session
     */
    async restoreSession() {
        console.log('🔄 Attempting session restoration...');

        try {
            // First, try to restore from stored credentials
            await this.restoreStoredCredentials();

            // Then check if we have valid session
            const credential = this.esriId.findCredential(this.portalUrl + "/sharing");
            if (credential) {
                console.log('✅ Found existing credential');
                await this.validateAndUseCredential(credential);
                return;
            }

            // Try checkSignInStatus as fallback
            await this.esriId.checkSignInStatus(this.portalUrl + "/sharing");
            console.log('✅ Active session found via checkSignInStatus');
            await this.handleSuccessfulAuth();

        } catch (error) {
            console.log('ℹ️ No active session found:', error.message);
            this.showSignedOutState();
        }
    },

    /**
     * Store credentials in localStorage for persistence
     */
    storeCredentials() {
        try {
            const credentials = Array.isArray(this.esriId.credentials) ? this.esriId.credentials : [];
            const credentialsData = credentials.map(cred => ({
                server: cred.server,
                token: cred.token,
                expires: cred.expires,
                userId: cred.userId,
                ssl: cred.ssl
            }));

            localStorage.setItem('erdb_auth_credentials', JSON.stringify(credentialsData));
            localStorage.setItem('erdb_auth_timestamp', Date.now().toString());
            
            console.log('💾 Credentials stored for persistence');
        } catch (error) {
            console.warn('Failed to store credentials:', error);
        }
    },

    /**
     * Restore credentials from localStorage
     */
    async restoreStoredCredentials() {
        try {
            const storedCredentials = localStorage.getItem('erdb_auth_credentials');
            const timestamp = localStorage.getItem('erdb_auth_timestamp');

            if (!storedCredentials || !timestamp) {
                return false;
            }

            // Check if credentials are not too old (24 hours)
            const age = Date.now() - parseInt(timestamp);
            if (age > 24 * 60 * 60 * 1000) {
                console.log('ℹ️ Stored credentials too old, clearing');
                this.clearStoredCredentials();
                return false;
            }

            const credentialsData = JSON.parse(storedCredentials);
            console.log(`🔄 Restoring ${credentialsData.length} stored credentials`);

            // Restore each credential
            for (const credData of credentialsData) {
                if (credData.expires && Date.now() < credData.expires && credData.server && credData.token) {
                    // Properly register token with IdentityManager instead of mutating internals
                    this.esriId.registerToken({
                        server: credData.server,
                        token: credData.token,
                        userId: credData.userId,
                        expires: credData.expires,
                        ssl: credData.ssl
                    });
                    console.log('✅ Registered token for:', credData.server);
                }
            }

            return true;
        } catch (error) {
            console.warn('Failed to restore credentials:', error);
            return false;
        }
    },

    /**
     * Clear stored credentials
     */
    clearStoredCredentials() {
        localStorage.removeItem('erdb_auth_credentials');
        localStorage.removeItem('erdb_auth_timestamp');
        localStorage.removeItem('erdb_authenticated_user');
        localStorage.removeItem('erdb_auth_timestamp');
    },

    /**
     * Validate and use an existing credential
     */
    async validateAndUseCredential(credential) {
        try {
            // Test the credential by making a simple request
            const testUrl = `${this.portalUrl}/sharing/rest/portals/self?f=json&token=${credential.token}`;
            const response = await fetch(testUrl);
            
            if (response.ok) {
                const data = await response.json();
                if (!data.error) {
                    console.log('✅ Credential validated successfully');
                    await this.handleSuccessfulAuth();
                    return true;
                }
            }
            
            console.log('❌ Credential validation failed, removing');
            this.esriId.destroyCredentials();
            return false;
            
        } catch (error) {
            console.warn('Credential validation error:', error);
            return false;
        }
    },

    /**
     * Handle successful authentication
     */
    async handleSuccessfulAuth() {
        try {
            const portal = new this.Portal({
                url: this.portalUrl
            });

            await portal.load();
            
            const userInfo = {
                username: portal.user.username,
                fullName: portal.user.fullName,
                email: portal.user.email,
                thumbnailUrl: portal.user.thumbnailUrl
            };

            this.currentUser = userInfo;
            
            // Store user info
            localStorage.setItem('erdb_authenticated_user', userInfo.username);
            localStorage.setItem('erdb_auth_timestamp', Date.now().toString());

            // Update UI
            if (typeof window.updateUIAuthenticationState === 'function') {
                window.updateUIAuthenticationState(true, userInfo);
            }

            // Show notification
            if (typeof window.showNotification === 'function') {
                window.showNotification(`Welcome back, ${userInfo.fullName || userInfo.username}!`, 'success', 4000);
            }

            console.log('✅ Authentication successful for:', userInfo.username);
            return userInfo;

        } catch (error) {
            console.error('Error in handleSuccessfulAuth:', error);
            this.showSignedOutState();
            throw error;
        }
    },

    /**
     * Show signed out state
     */
    showSignedOutState() {
        this.currentUser = null;
        
        if (typeof window.updateUIAuthenticationState === 'function') {
            window.updateUIAuthenticationState(false);
        }
    },

    /**
     * Sign in user using direct redirect flow
     */
    async signIn() {
        try {
            console.log('🔐 Initiating sign in with redirect flow...');
            
            if (typeof window.showNotification === 'function') {
                window.showNotification('Redirecting to DENR Portal sign-in...', 'info', 2000);
            }

            // Use getCredential with redirect flow - no popup
            await this.esriId.getCredential(this.portalUrl + "/sharing", {
                oAuthPopupConfirmation: false, // Skip popup confirmation
                error: (error) => {
                    console.error('OAuth error:', error);
                }
            });

            console.log('✅ Sign in successful');
            
            // Store credentials for persistence
            this.storeCredentials();
            
            // Handle successful auth
            await this.handleSuccessfulAuth();

        } catch (error) {
            console.error('Sign in failed:', error);
            
            let errorMessage = 'Sign in failed. Please try again.';
            if (error.name === 'AbortError') {
                errorMessage = 'Sign in timed out. Please try again.';
            } else if (error.message.includes('User cancelled')) {
                errorMessage = 'Sign in cancelled by user.';
            }

            if (typeof window.showNotification === 'function') {
                window.showNotification(errorMessage, 'error', 5000);
            }

            throw error;
        }
    },

    /**
     * Sign out user
     */
    async signOut() {
        try {
            console.log('👋 Signing out user...');
            
            if (typeof window.showNotification === 'function') {
                window.showNotification('Signing out...', 'info', 2000);
            }

            // Destroy credentials
            this.esriId.destroyCredentials();
            
            // Clear stored data
            this.clearStoredCredentials();
            
            // Update state
            this.showSignedOutState();

            console.log('✅ Sign out successful');

        } catch (error) {
            console.error('Sign out error:', error);
        }
    },

    /**
     * Check if user is currently signed in
     */
    isSignedIn() {
        try {
            const credential = this.esriId.findCredential(this.portalUrl + "/sharing");
            return !!credential && !!this.currentUser;
        } catch (error) {
            return false;
        }
    },

    /**
     * Get current user info
     */
    getCurrentUser() {
        return this.currentUser;
    }
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}

// Make available globally
if (typeof window !== 'undefined') {
    window.AuthManager = AuthManager;
}
