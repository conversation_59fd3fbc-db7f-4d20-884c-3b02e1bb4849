/**
 * TypeScript definitions for ERDB Control Map
 * Provides type safety and better IDE support
 */

// Global interfaces
interface Window {
    SecurityUtils: typeof SecurityUtils;
    AppConfig: typeof AppConfig;
    ModuleLoader: typeof ModuleLoader;
    ErrorManager: typeof ErrorManager;
    showNotification: (message: string, type?: NotificationType, duration?: number) => HTMLElement;
    openConfiguration: () => void;
    populateFilterDropdowns: () => void;
    toggleFilterPanel: () => void;
    applyFilters: () => void;
    clearFilters: () => void;
    applyLayerFilters: () => void;
    updateFilterStatus: () => void;
    updateLayerStatus: (featureCount: number, layerName?: string) => void;
    zoomToAllBuildings: () => void;
    refreshLayerData: () => void;
    exportVisibleFeatures: () => void;
    globalFeatureLayer?: any;
    globalFeatureLayerView?: any;
    ERDB_BUNDLED_MODE?: boolean;
}

// Notification types
type NotificationType = 'info' | 'success' | 'warning' | 'error';

// Configuration interfaces
interface AppConfiguration {
    app: AppSettings;
    map: MapSettings;
    services: ServiceSettings;
    authentication: AuthenticationSettings;
    layout: LayoutSettings;
    popupTemplate: PopupTemplate;
    tableTemplate: TableTemplate;
}

interface AppSettings {
    title: string;
    description: string;
    logoHeading: string;
    signOutDescription: string;
    signInDescription: string;
}

interface MapSettings {
    center: [number, number];
    zoom: number;
    basemap: string;
    alternateBasemap: string;
}

interface ServiceSettings {
    portalUrl: string;
    featureLayerUrl: string;
}

interface AuthenticationSettings {
    appId: string;
    requireAuth: boolean;
    authMode: 'oauth' | 'token' | 'credentials';
    username: string;
    password: string | EncryptedData;
    token: string;
    tokenExpiration: string | null;
    autoSignIn: boolean;
    encrypted?: boolean;
}

interface LayoutSettings {
    mapHeight: string;
    tableHeight: string;
}

interface PopupTemplate {
    title: string;
    fieldInfos: FieldInfo[];
}

interface TableTemplate {
    columnTemplates: ColumnTemplate[];
}

interface FieldInfo {
    fieldName: string;
    label: string;
    visible?: boolean;
    format?: {
        digitSeparator?: boolean;
        places?: number;
    };
}

interface ColumnTemplate {
    type: string;
    fieldName: string;
    label: string;
    direction?: 'asc' | 'desc';
}

// Security interfaces
interface EncryptedData {
    encrypted: number[];
    salt: number[];
    iv: number[];
}

interface ValidationUtils {
    url(input: string): string | null;
    text(input: string, maxLength?: number): string;
    number(input: any, min?: number, max?: number): number | null;
    coordinates(input: any): [number, number] | null;
}

// Error management interfaces
interface ErrorInfo {
    type: string;
    message: string;
    filename?: string;
    lineno?: number;
    colno?: number;
    stack?: string;
    timestamp: string;
    code?: string;
    details?: any;
}

interface MemoryUsage {
    used: number;
    total: number;
    limit: number;
    timestamp: number;
}

interface ErrorStats {
    totalErrors: number;
    errorTypes: Record<string, number>;
    recentErrors: ErrorInfo[];
    memoryUsage?: MemoryUsage;
}

interface DisposableResource {
    resource: any;
    dispose: () => void;
}

// Layer discovery interfaces
interface LayerDiscoveryResult {
    tableTemplate: TableTemplate;
    popupTemplate: PopupTemplate;
    layerInfo: LayerInfo;
}

interface LayerInfo {
    name: string;
    description: string;
    geometryType: string;
    fields: FieldDefinition[];
    capabilities: string;
}

interface FieldDefinition {
    name: string;
    type: string;
    alias?: string;
    length?: number;
}

// Filter interfaces
interface FilterValues {
    office_level_2: string;
    region: string;
}

// Module definitions
declare const SecurityUtils: {
    encryptData(data: string, password: string): Promise<EncryptedData>;
    decryptData(encryptedData: EncryptedData, password: string): Promise<string>;
    validateAndSanitize: ValidationUtils;
};

declare const AppConfig: {
    default: AppConfiguration;
    load(): AppConfiguration;
    save(config: AppConfiguration): Promise<boolean>;
    validateConfiguration(config: AppConfiguration): AppConfiguration;
    getDeviceId(): Promise<string>;
    reset(): boolean;
    applyUrlOverrides(config: AppConfiguration): AppConfiguration;
    discoverFields(layerUrl: string, authToken?: string): Promise<LayerDiscoveryResult>;
};

declare const ModuleLoader: {
    loadedModules: Set<string>;
    dependencies: Record<string, string[]>;
    loadModule(moduleName: string): Promise<void>;
    isModuleAvailable(moduleName: string): boolean;
    loadScript(src: string): Promise<void>;
    initializeApp(modules: string[], callback?: () => void): Promise<void>;
};

declare const ErrorManager: {
    errorLog: ErrorInfo[];
    maxLogSize: number;
    disposables: Set<DisposableResource>;
    memoryWatchers: Map<string, MemoryUsage>;
    init(): void;
    setupGlobalErrorHandling(): void;
    setupMemoryMonitoring(): void;
    setupUnloadHandlers(): void;
    logError(errorInfo: ErrorInfo): void;
    showUserErrorNotification(errorInfo: ErrorInfo): void;
    attemptErrorRecovery(errorInfo: ErrorInfo): void;
    registerDisposable(resource: any, disposeFunc: () => void): void;
    suggestMemoryCleanup(): void;
    cleanupCachedData(): void;
    pauseNonCriticalOperations(): void;
    resumeOperations(): void;
    cleanup(): void;
    getErrorStats(): ErrorStats;
    handleArcGISError(esriError: any): void;
};

// Export for module use
export {
    AppConfiguration,
    AppSettings,
    MapSettings,
    ServiceSettings,
    AuthenticationSettings,
    LayoutSettings,
    PopupTemplate,
    TableTemplate,
    FieldInfo,
    ColumnTemplate,
    EncryptedData,
    ValidationUtils,
    ErrorInfo,
    MemoryUsage,
    ErrorStats,
    DisposableResource,
    LayerDiscoveryResult,
    LayerInfo,
    FieldDefinition,
    FilterValues,
    NotificationType
};
