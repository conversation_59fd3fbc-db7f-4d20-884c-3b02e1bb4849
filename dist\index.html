<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
    <title id="page-title">DENR Control Map</title>
    <!-- Prevent favicon 500 errors by providing a data URL favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
    <style>
        html, body, #viewDiv {
            padding: 0;
            margin: 0;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }
        
        /* Responsive layout system */
        #mapContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: var(--map-height, 70%);
            transition: height 0.3s ease;
        }
        
        
        /* Full-screen map layout */
        #mapContainer {
            height: 100% !important;
        }
        
        /* Enhanced Mobile responsiveness with Calcite */
        @media (max-width: 768px) {
            /* Touch-friendly interface */
            .esri-ui-top-left .esri-component,
            .esri-ui-top-right .esri-component {
                margin: 8px;
            }
            
            /* Larger touch targets */
            .esri-widget button,
            .esri-widget .esri-widget__button {
                min-height: 44px;
                min-width: 44px;
            }

            /* Mobile filter panel adjustments */
            calcite-shell-panel[slot="panel-start"] {
                --calcite-shell-panel-width: 90vw;
                --calcite-shell-panel-max-width: 350px;
            }
            
            /* Mobile navigation improvements */
            calcite-navigation {
                --calcite-navigation-height: 60px;
            }
            
            /* Hide title on very small screens */
            #main-title {
                display: none;
            }
        }

        @media (max-width: 480px) {
            /* Very small screens - compact filter controls */
            calcite-card {
                --calcite-card-spacing: 0.75rem;
            }
            
            calcite-label {
                margin-bottom: 0.75rem !important;
            }
        }
        
        /* Calcite loading states */
        .calcite-loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(3px);
        }
        
        .calcite-loading-content {
        text-align: center;
            padding: 2rem;
            background: var(--calcite-color-foreground-1);
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            max-width: 300px;
        }
        
        /* Calcite notification container */
        .calcite-notification-container {
            position: fixed;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            max-width: 90%;
            width: auto;
            min-width: 300px;
        }

        /* Enhanced Calcite integration */
        calcite-notice {
            margin-bottom: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        /* DENR Theme Integration */
        :root {
            --calcite-color-brand: #2E7D32;
            --calcite-color-brand-hover: #1B5E20;
            --calcite-color-brand-press: #388E3C;
        }

        /* Enhanced navigation styling */
        calcite-navigation {
            --calcite-navigation-user-display: flex;
            --calcite-navigation-user-alignment: center;
        }

        /* Ensure navigation actions are properly spaced */
        calcite-navigation [slot="user"] {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Configuration button styling when visible */
        #config-button[style*="inline-flex"] {
            display: inline-flex !important;
        }

        /* Enhanced sign-in button styling */
        #sign-in-button {
            transition: all 0.3s ease;
        }

        #sign-in-button[loading] {
            --calcite-color-brand: #66BB6A;
            cursor: wait;
        }

        #sign-in-button[disabled] {
            opacity: 0.7;
            pointer-events: none;
        }

        /* Custom login notification styling */
        .login-notification {
            --calcite-notice-spacing: 1rem;
        }
        .container {
            height: 100%;
            width: 100%;
      }

    
    </style>
    <link rel="stylesheet" href="https://js.arcgis.com/4.33/esri/themes/light/main.css" />
    <link rel="stylesheet" type="text/css" href="https://js.arcgis.com/calcite-components/2.13.2/calcite.css" />
    <script src="https://js.arcgis.com/4.33/"></script>
    <script type="module" src="https://js.arcgis.com/calcite-components/2.13.2/calcite.esm.js"></script>
    <script type="module" src="https://js.arcgis.com/map-components/4.33/index.js"></script>
    <!-- Module loading scripts -->
    
    
    
    
    
    
    <script>

        // AppConfig is now loaded from external module
        // Note: AppConfig is now available from js/app-config.js

        // Configuration loader utility with enhanced validation
        async function loadConfiguration() {
            let config = AppConfig.load();
            config = AppConfig.applyUrlOverrides(config);
            
            // Decrypt password if encrypted
            if (config.authentication && config.authentication.encrypted && config.authentication.password) {
                try {
                    const deviceId = await AppConfig.getDeviceId();
                    const decryptedPassword = await SecurityUtils.decryptData(config.authentication.password, deviceId);
                    config.authentication.password = decryptedPassword;
                    config.authentication.encrypted = false; // Mark as decrypted for current session
                } catch (error) {
                    console.warn('Could not decrypt stored password:', error);
                    config.authentication.password = '';
                    showNotification('Stored credentials could not be decrypted. Please re-enter your password.', 'warning');
                }
            }
            
            // Log configuration source
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('debug') === 'true') {
                // Don't log sensitive information
                const safeConfig = { ...config };
                if (safeConfig.authentication && safeConfig.authentication.password) {
                    safeConfig.authentication.password = '[ENCRYPTED]';
                }
                console.log('Final configuration:', safeConfig);
            }
            
            return config;
        }

        // Auto-diagnostic function to detect and fix common issues
        function runStartupDiagnostics(config) {
            console.log('Running startup diagnostics...');
            
            let issuesFound = [];
            let autoFixes = [];
            let configUpdated = false;
            
            // Check for MapServer URL that should be FeatureServer
            if (config.services.featureLayerUrl && config.services.featureLayerUrl.includes('MapServer')) {
                issuesFound.push('MapServer URL detected (should be FeatureServer for editing)');
                const correctedUrl = config.services.featureLayerUrl.replace('MapServer', 'FeatureServer');
                config.services.featureLayerUrl = correctedUrl;
                autoFixes.push(`Auto-corrected: MapServer → FeatureServer (${correctedUrl})`);
                configUpdated = true;
            }
            
            // Check for old portal URLs (only the old erdb subdomain)
            if (config.services.portalUrl && config.services.portalUrl.includes('controlmap-erdb.denr.gov.ph')) {
                issuesFound.push('Old portal URL detected (controlmap-erdb)');
                config.services.portalUrl = AppConfig.default.services.portalUrl;
                autoFixes.push('Auto-corrected: Updated from controlmap-erdb to controlmap');
                configUpdated = true;
            }
            
            // Save configuration if any fixes were applied
            if (configUpdated) {
                AppConfig.save(config);
            }
            
            // Report diagnostics
            if (issuesFound.length > 0) {
                console.warn('Startup issues detected:', issuesFound);
                console.log('Auto-fixes applied:', autoFixes);
                
                // Show notification about auto-fixes
                setTimeout(() => {
                    showNotification(
                        `🔧 Auto-fixes applied: ${autoFixes.join(', ')}`,
                        'success',
                        6000
                    );
                }, 2000);
            } else {
                console.log('✅ No configuration issues detected');
            }
            
            return config;
        }

        // Wait for DOM to be fully loaded before initializing
        function initializeApplication() {
        loadConfiguration().then((config) => {
                try {
                    // Run startup diagnostics and auto-fixes
                    config = runStartupDiagnostics(config);
                    // Update HTML elements with configuration values (with null checks)
                    const pageTitle = document.getElementById('page-title');
                    if (pageTitle) pageTitle.textContent = config.app.title;
                    
                    const mainTitle = document.getElementById('main-title');
                    if (mainTitle) mainTitle.textContent = config.app.title;
                    
                    const navLogo = document.getElementById('nav-logo');
                    if (navLogo) {
                        navLogo.setAttribute('heading', config.app.title);
                        navLogo.setAttribute('description', config.app.description);
                    }
                    
                    // Full-screen map layout - no additional CSS properties needed
                    
                    // Initialize ArcGIS components after DOM elements are ready
                    initializeArcGISComponents(config);
                    
                } catch (error) {
                    console.error('Error during application initialization:', error);
                    showNotification('Application initialization failed. Please refresh the page.', 'error');
                }
            }).catch((error) => {
                console.error('Error loading configuration:', error);
                showNotification('Configuration loading failed. Using default settings.', 'warning');
                // Fallback to default configuration
                initializeArcGISComponents(AppConfig.default);
            });
        }

        // Initialize ArcGIS components
        function initializeArcGISComponents(config) {
            require([
                "esri/config",
                "esri/Map",
                "esri/views/MapView",
                "esri/core/reactiveUtils",
                "esri/request",
                "esri/layers/FeatureLayer",
                "esri/Graphic",
                "esri/widgets/Editor",
                "esri/portal/Portal",
                "esri/identity/OAuthInfo",
                "esri/identity/IdentityManager",
                "esri/portal/PortalQueryParams",
                "esri/core/Error"
            ], (esriConfig, Map, MapView, reactiveUtils, request, FeatureLayer, Graphic, Editor, Portal, OAuthInfo, esriId, PortalQueryParams, EsriError) => {

                let selectionIdCount = 0; // The filtered selection id count
                let candidate; // The graphic accessed via the view.click event
                let featureLayerView;

                // Use configuration values
                const portalUrl = config.services.portalUrl;
                const featureLayerUrl = config.services.featureLayerUrl;

                console.log('Loading feature layer from URL:', featureLayerUrl);

                esriConfig.portalUrl = portalUrl;
        
            // Upload functionality removed

            const map = new Map({
                basemap: config.map.basemap
            });
/*
            const view = new MapView({
                center: [122, 12],
                zoom: 6,
                map: map,
                container: "mapContainer",
                popup: {
                    defaultPopupTemplateEnabled: true
                }
            });
*/
             const view = new MapView({
                center: config.map.center,
                zoom: config.map.zoom,
                map: map,
                container: "mapContainer"
            });

            const popupTemplate = {
                title: config.popupTemplate.title,
                content: [{
                    type: "fields",
                    fieldInfos: config.popupTemplate.fieldInfos
                }]
            };
             
        
            // File upload functionality removed

            // Enhanced error handling with user-friendly notifications
            function errorHandler(error) {
                console.error('Application error:', error);
                
                // Show notification
                showNotification(error.message, 'error');
            }
            
            // Modern Calcite notification system
            function showNotification(message, type = 'info', duration = 5000) {
                // Create notification container if it doesn't exist
                let container = document.querySelector('.calcite-notification-container');
                if (!container) {
                    container = document.createElement('div');
                    container.className = 'calcite-notification-container';
                    document.body.appendChild(container);
                }

                // Map types to Calcite notice kinds
                const kindMap = {
                    'info': 'brand',
                    'success': 'success', 
                    'warning': 'warning',
                    'error': 'danger'
                };

                // Map types to icons
                const iconMap = {
                    'info': 'information',
                    'success': 'check-circle',
                    'warning': 'exclamation-mark-triangle',
                    'error': 'exclamation-mark-triangle-f'
                };

                // Create Calcite notice
                const notice = document.createElement('calcite-notice');
                notice.setAttribute('open', '');
                notice.setAttribute('kind', kindMap[type] || 'brand');
                notice.setAttribute('icon', iconMap[type] || 'information');
                notice.setAttribute('auto-close', '');
                notice.setAttribute('auto-close-duration', duration.toString());

                // Create notice message
                const noticeMessage = document.createElement('div');
                noticeMessage.setAttribute('slot', 'message');
                noticeMessage.textContent = SecurityUtils.validateAndSanitize.text(message);
                notice.appendChild(noticeMessage);

                container.appendChild(notice);

                // Auto-remove from DOM after animation
                setTimeout(() => {
                    if (notice.parentNode) {
                        notice.remove();
                    }
                }, duration + 500);
                
                return notice;
            }
            
            // Modern Calcite loading overlay
            function showLoadingOverlay(container = document.body, message = 'Loading...') {
                const overlay = document.createElement('div');
                overlay.className = 'calcite-loading-overlay';
                
                const content = document.createElement('div');
                content.className = 'calcite-loading-content';
                
                const loader = document.createElement('calcite-loader');
                loader.setAttribute('active', '');
                loader.setAttribute('label', SecurityUtils.validateAndSanitize.text(message));
                loader.setAttribute('text', SecurityUtils.validateAndSanitize.text(message));
                
                content.appendChild(loader);
                overlay.appendChild(content);
                container.appendChild(overlay);
                
                return overlay;
            }
            
            function hideLoadingOverlay(overlay) {
                if (overlay && overlay.parentNode) {
                    overlay.remove();
                }
            }

            // Shapefile upload functionality removed

            // Modern Basemap Toggle using web component
            const basemapToggle = document.createElement("arcgis-basemap-toggle");
            basemapToggle.view = view;
            basemapToggle.nextBasemap = config.map.alternateBasemap;

            view.ui.add(basemapToggle, "bottom-right");

            // Enhanced Feature Layer with conditional clustering
            var featureLayer = new FeatureLayer({
                url: featureLayerUrl,
                outFields: ["*"], // Include all fields in feature attributes
                popupTemplate: popupTemplate,
                // Performance optimizations
                maxRecordCountFactor: 2,
                minScale: 0,
                maxScale: 0
                // Note: Feature reduction (clustering) will be added conditionally based on feature count
            });

            // Add error handling for feature layer with improved notifications
            featureLayer.when(() => {
                console.log("Feature layer loaded successfully:", featureLayer.title || featureLayer.url);
                
                // Log layer capabilities for debugging
                if (config.debug || new URLSearchParams(window.location.search).get('debug') === 'true') {
                    console.log("Layer capabilities:", featureLayer.capabilities);
                    console.log("Layer fields:", featureLayer.fields);
                    console.log("Layer geometry type:", featureLayer.geometryType);
                }
                
                // Show success notification
                showNotification("Feature layer loaded successfully", "success", 3000);
                
                // Configure performance-based rendering
                if (featureLayer.capabilities && featureLayer.capabilities.query) {
                    featureLayer.queryFeatureCount().then(count => {
                        console.log(`Feature layer contains ${count} features`);
                        
                        // Update layer status indicator
                        window.updateLayerStatus(count, featureLayer.title || 'DENR Administrative Buildings');
                        
                        // Only enable clustering for very large datasets (>1000 features)
                        // With 83 features, we'll show actual geometries
                        if (count > 1000) {
                            console.log("Large dataset detected - enabling clustering for performance");
                            featureLayer.featureReduction = {
                                type: "cluster",
                                clusterRadius: count > 5000 ? "150px" : "100px",
                                clusterMinSize: "24px",
                                clusterMaxSize: "60px",
                                labelingInfo: [{
                                    deconflictionStrategy: "none",
                                    labelExpressionInfo: {
                                        expression: "Text($feature.cluster_count, '#,###')"
                                    },
                                    symbol: {
                                        type: "text",
                                        color: "#004c73",
                                        font: {
                                            weight: "bold",
                                            family: "Noto Sans",
                                            size: "12px"
                                        }
                                    },
                                    labelPlacement: "center-center"
                                }]
                            };
                            
                            // Add clustering toggle button
                            addClusteringToggle(featureLayer, count);
                        } else {
                            console.log("Small dataset - showing actual feature geometries");
                            showNotification(`Displaying ${count} features with actual geometries 🗺️`, 'success', 4000);
                        }
                    }).catch(e => console.warn("Could not query feature count:", e));
                }
                
            }).catch((error) => {
                console.error("Error loading feature layer:", error);
                
                // Check if this is a MapServer URL issue
                let errorMessage = error.message || 'Unknown error';
                let fixSuggestion = '';
                
                if (featureLayerUrl.includes('MapServer')) {
                    fixSuggestion = ' This appears to be a MapServer URL - try using FeatureServer instead for editing capabilities.';
                    
                    // Auto-attempt to fix MapServer to FeatureServer
                    const correctedUrl = featureLayerUrl.replace('MapServer', 'FeatureServer');
                    console.warn('Attempting to auto-correct MapServer URL to FeatureServer:', correctedUrl);
                    
                    // Update configuration with corrected URL
                    const currentConfig = AppConfig.load();
                    currentConfig.services.featureLayerUrl = correctedUrl;
                    AppConfig.save(currentConfig);
                    
                    showNotification(
                        `Auto-correcting URL: MapServer → FeatureServer. Please refresh the page to try the corrected URL.`,
                        'warning',
                        8000
                    );
                } else if (errorMessage.includes('not found') || errorMessage.includes('404')) {
                    fixSuggestion = ' The service may be unavailable or the URL may be incorrect. Please check the configuration.';
                } else if (errorMessage.includes('CORS') || errorMessage.includes('Access-Control')) {
                    fixSuggestion = ' This appears to be a CORS issue. Try clearing the cache or updating the portal URL.';
                }
                
                // Show user-friendly error notification
                showNotification(
                    `Layer Loading Error: Unable to load feature layer from ${featureLayerUrl}. ${errorMessage}${fixSuggestion}`,
                    'error',
                    15000
                );
                
                // If this is a MapServer issue and auto-correction was applied, show refresh button
                if (featureLayerUrl.includes('MapServer')) {
                setTimeout(() => {
                        if (confirm('The URL has been auto-corrected from MapServer to FeatureServer. Would you like to refresh the page now to apply the fix?')) {
                            window.location.reload();
                    }
                    }, 2000);
                }
            });

            map.add(featureLayer);

            // Enhanced Editor widget
            var editor = new Editor({
                view: view,
                allowedWorkflows: ["create", "update", "delete"], // Specify allowed operations
                visibleElements: {
                    createWorkflow: {
                        creationMode: "single"
                    }
                }
            });

            view.ui.add(editor, "top-right");

            // Search widget removed for simplified interface

            // LayerList and Legend widgets removed for simplified interface


            // Feature table removed for simplified map-only interface

        
            // Enhanced OAuth 2.0 authentication using AuthManager
            console.log('🔐 Initializing enhanced authentication...');

            // UI elements
            const signInButton = document.getElementById("sign-in-button");
            const navLogo = document.getElementById("nav-logo");
            const navigationUser = document.getElementById("nav-user");
            
            // Initialize AuthManager with esriId, config, and required classes
            if (typeof AuthManager !== 'undefined') {
                const authInitialized = AuthManager.init(esriId, config, {
                    OAuthInfo: OAuthInfo,
                    Portal: Portal
                });
                
                if (authInitialized) {
                    // Setup sign in/out handlers
                    if (signInButton) {
                        signInButton.addEventListener("click", async () => {
                            if (AuthManager.isSignedIn()) {
                                await AuthManager.signOut();
                            } else {
                                await AuthManager.signIn();
                            }
                        });
                    }
                    
                    if (navigationUser) {
                        navigationUser.addEventListener("click", async () => {
                            if (AuthManager.isSignedIn()) {
                                await AuthManager.signOut();
                            } else {
                                await AuthManager.signIn();
                            }
                        });
                    }
                    
                    console.log('✅ Enhanced authentication initialized');
                } else {
                    console.warn('AuthManager initialization failed, falling back to legacy auth');
                    legacyAuthSetup();
                }
            } else {
                console.warn('AuthManager not available, falling back to legacy auth');
                // Fallback to original authentication logic
                legacyAuthSetup();
            }

            function legacyAuthSetup() {
                // Original authentication code as fallback
                const loginPortalUrl = portalUrl.endsWith('/home') ? portalUrl : portalUrl + '/home';
                
                const info = new OAuthInfo({
                    appId: config.authentication.appId,
                    portalUrl: loginPortalUrl,
                    popup: true, // Use popup flow for better session persistence
                    flowType: "auto",
                    preserveUrlHash: true
                });

                signInButton.addEventListener("click", signInOrOut);
                navigationUser.addEventListener("click", signInOrOut);
                
                esriId.registerOAuthInfos([info]);
                esriId.useSignInPage = false;
                esriId.tokenDuration = 20160;
                    
                    checkSignIn();
            }

            // Function to store authenticated user info in localStorage
            function storeAuthenticatedUser(username) {
                if (username) {
                    localStorage.setItem('erdb_authenticated_user', username);
                    localStorage.setItem('erdb_auth_timestamp', Date.now().toString());
                    console.log(`Stored authenticated user: ${username}`);
                } else {
                    localStorage.removeItem('erdb_authenticated_user');
                    localStorage.removeItem('erdb_auth_timestamp');
                    console.log('Cleared authenticated user storage');
                }
            }

            // Enhanced function to check the current sign in status and query the portal if signed in.
            function checkSignIn() {
                console.log('🔍 Checking sign-in status...');
                
                // First try to find existing credentials
                const existingCredentials = esriId.findCredential(portalUrl + "/sharing");
                if (existingCredentials) {
                    console.log('✅ Found existing credentials - user should be authenticated');
                }
                
                esriId
                    .checkSignInStatus(portalUrl + "/sharing")
                    .then(() => {
                        console.log('✅ User is signed in - loading portal...');
                        
                        // NOTE: Don't update UI elements directly here - use centralized function instead
                        
                        const portal = new Portal({
                            authMode: "immediate",
                            url: portalUrl // Use the base portal URL for API calls, not the login URL
                        });
                        
                        // Load the portal, display the name and username, then call the query items function.
                        portal.load().then(() => {
                            console.log('🌐 Portal loaded successfully');
                            console.log('👤 User info:', {
                                username: portal.user.username,
                                fullName: portal.user.fullName,
                                email: portal.user.email
                            });
                            
                            // Use centralized UI state management
                            const userInfo = {
                                username: portal.user.username,
                                fullName: portal.user.fullName,
                                email: portal.user.email,
                                thumbnailUrl: portal.user.thumbnailUrl
                            };
                            
                            updateUIAuthenticationState(true, userInfo);
                            
                            if (navLogo) {
                            navLogo.description = config.app.signOutDescription;
                            }
                            
                            // Store authenticated user info in localStorage
                            storeAuthenticatedUser(userInfo.username);
                            
                            queryItems(portal);
                            
                            // Show welcome notification
                            showNotification(`Welcome back, ${userInfo.fullName || userInfo.username}!`, 'success', 4000);
                            
                        }).catch(error => {
                            console.error('❌ Error loading portal:', error);
                            console.error('Portal error details:', {
                                message: error.message,
                                stack: error.stack,
                                name: error.name
                            });
                            showNotification('Error loading user profile', 'error', 3000);
                            updateUIAuthenticationState(false);
                        });
                    })
                    .catch((error) => {
                        console.log('ℹ️ User not signed in:', error.message || 'No active session');
                        console.log('Authentication error details:', {
                            message: error.message,
                            name: error.name,
                            type: typeof error
                        });
                        
                        // Use centralized UI state management
                        updateUIAuthenticationState(false);
                        
                        if (navLogo) {
                            navLogo.description = config.app.signInDescription;
                        }
                        
                        // Clear stored authentication
                        storeAuthenticatedUser(null);
                    });
            }

            // Configuration access control
            const AUTHORIZED_CONFIG_USERS = ['afolvida'];
            let currentUser = null;
            
            // Enhanced UI state management with robust error handling
            function updateUIAuthenticationState(isAuthenticated, userInfo = null) {
                console.log('🎯 Updating UI authentication state:', { isAuthenticated, userInfo });
                
                const signInButton = document.getElementById("sign-in-button");
                const navigationUser = document.getElementById("nav-user");
                
                if (isAuthenticated && userInfo) {
                    // User is authenticated - show user info, hide sign in
                    if (navigationUser) {
                        navigationUser.hidden = false;
                        navigationUser.style.display = 'flex';
                        navigationUser.fullName = userInfo.fullName;
                        navigationUser.username = userInfo.username;
                        
                        if (userInfo.thumbnailUrl) {
                            navigationUser.thumbnail = userInfo.thumbnailUrl;
                        }
                        console.log('✅ Navigation user updated with:', userInfo);
                    } else {
                        console.warn('⚠️ Navigation user element not found');
                    }
                    
                    if (signInButton) {
                        signInButton.hidden = true;
                        signInButton.style.display = 'none';
                        console.log('✅ Sign-in button hidden');
                    } else {
                        console.warn('⚠️ Sign-in button element not found');
                    }
                    
                    // Update current user for config access - with validation
                    if (userInfo.username) {
                        currentUser = userInfo.username;
                        console.log('✅ Current user set to:', currentUser);
                        updateConfigButtonVisibility();
                        
                        // Force config button visibility update after small delay to ensure DOM is ready
                        setTimeout(() => {
                            updateConfigButtonVisibility();
                        }, 100);
                    } else {
                        console.warn('⚠️ User info missing username');
                    }
                    
                } else {
                    // User is not authenticated - show sign in, hide user info
                    if (signInButton) {
                        signInButton.hidden = false;
                        signInButton.style.display = 'inline-flex';
                        console.log('✅ Sign-in button shown');
                    }
                    
                    if (navigationUser) {
                        navigationUser.hidden = true;
                        navigationUser.style.display = 'none';
                        console.log('✅ Navigation user hidden');
                    }
                    
                    // Clear current user
                    currentUser = null;
                    console.log('✅ Current user cleared');
                    updateConfigButtonVisibility();
                }
                
                // Debug: Log final UI state
                console.log('🎯 Final UI state:', {
                    signInButtonVisible: signInButton ? !signInButton.hidden : 'element not found',
                    navigationUserVisible: navigationUser ? !navigationUser.hidden : 'element not found',
                    currentUser: currentUser
                });
            }
            
            function updateConfigButtonVisibility() {
                const configButton = document.getElementById('config-button');
                console.log('🔧 Updating config button visibility...');
                console.log('Config button element found:', !!configButton);
                console.log('Current user:', currentUser);
                console.log('Authorized users:', AUTHORIZED_CONFIG_USERS);
                
                if (!configButton) {
                    console.error('❌ Config button element not found in DOM');
                    return;
                }
                
                if (currentUser) {
                    const isAuthorized = AUTHORIZED_CONFIG_USERS.includes(currentUser.toLowerCase());
                    console.log(`🔍 Authorization check for ${currentUser}: ${isAuthorized}`);
                    
                    if (isAuthorized) {
                        configButton.style.display = 'inline-flex'; // Use inline-flex for better alignment
                        configButton.title = `Configuration (Admin: ${currentUser})`;
                        console.log(`✅ Configuration access GRANTED for user: ${currentUser}`);
                        
                        // Only show notification once per session to avoid spam
                        if (!window.configAccessNotificationShown) {
                            showNotification(`🔧 Configuration access enabled for admin ${currentUser}`, 'success', 3000);
                            window.configAccessNotificationShown = true;
                        }
                    } else {
                        configButton.style.display = 'none';
                        console.log(`❌ Configuration access DENIED for user: ${currentUser} (not in authorized list)`);
                    }
                } else {
                    configButton.style.display = 'none';
                    console.log('🔒 Configuration button hidden - no user authenticated');
                }
                
                // Debug: Final button state
                console.log('🔧 Config button final state:', {
                    display: configButton.style.display,
                    visible: configButton.style.display !== 'none',
                    title: configButton.title
                });
            }

            // Query items function for portal integration
            function queryItems(portal) {
                try {
                    const queryParams = {
                        query: "owner:" + portal.user.username,
                        sortField: "modified",
                        sortOrder: "desc",
                        num: 20
                    };

                    portal.queryItems(queryParams).then((response) => {
                        console.log("User items:", response.results);
                        
                        // Update item gallery if it exists
                        const itemGallery = document.getElementById("item-gallery");
                        if (itemGallery && response.results.length > 0) {
                            itemGallery.innerHTML = '<h3>Recent Items</h3>';
                            response.results.slice(0, 5).forEach(item => {
                                const itemDiv = document.createElement('div');
                                itemDiv.style.cssText = 'padding: 8px; border-bottom: 1px solid #eee;';
                                itemDiv.innerHTML = `
                                    <strong>${SecurityUtils.validateAndSanitize.text(item.title)}</strong><br>
                                    <small>${SecurityUtils.validateAndSanitize.text(item.type)} - ${new Date(item.modified).toLocaleDateString()}</small>
                                `;
                                itemGallery.appendChild(itemDiv);
                            });
                        }
                        
                        showNotification(`Found ${response.results.length} items in your portal`, 'info', 3000);
                    }).catch((error) => {
                        console.error("Error querying portal items:", error);
                        showNotification("Could not load portal items", 'warning', 3000);
                    });
                } catch (error) {
                    console.error("Error in queryItems function:", error);
                }
            }

            // Enhanced function to sign in or out of the portal
            function signInOrOut() {
                console.log('🔄 Sign in/out button clicked...');
                
                esriId
                    .checkSignInStatus(portalUrl + "/sharing")
                    .then(() => {
                        // User is already signed in - sign them out
                        console.log('👋 Signing out user...');
                        showNotification('Signing out...', 'info', 2000);
                        
                        esriId.destroyCredentials();
                        currentUser = null;
                        
                        // Clear stored authentication
                        storeAuthenticatedUser(null);
                        
                        // Update UI immediately
                        updateUIAuthenticationState(false);
                        
                        // Reload page to ensure clean state
                        setTimeout(() => {
                		window.location.reload();
                        }, 1000);
                    })
                    .catch(() => {
                        // User is not signed in - initiate sign in
                        console.log('🔐 Initiating sign in process...');
                        showNotification('Opening DENR Portal sign-in...', 'info', 3000);
                        
                        // Show loading state
                        const signInButton = document.getElementById("sign-in-button");
                        if (signInButton) {
                            signInButton.loading = true;
                            signInButton.disabled = true;
                        }
                        
                        esriId
                            .getCredential(info.portalUrl + "/sharing", {
                                oAuthPopupConfirmation: false, // Skip confirmation dialog
                                signal: AbortSignal.timeout(60000) // 60 second timeout for redirect flow
                            })
                            .then(() => {
                                console.log('✅ Login successful - checking user status...');
                                showNotification('Login successful! Loading your profile...', 'success', 3000);
                                
                                // Reset button state
                                if (signInButton) {
                                    signInButton.loading = false;
                                    signInButton.disabled = false;
                                }
                                
                                // Check sign in status to update UI
                                checkSignIn();
                            })
                            .catch((error) => {
                                console.error('❌ Login failed:', error);
                                
                                // Reset button state
                                if (signInButton) {
                                    signInButton.loading = false;
                                    signInButton.disabled = false;
                                }
                                
                                if (error.name === 'AbortError') {
                                    showNotification('Login timed out. Please try again.', 'warning', 5000);
                                } else if (error.message.includes('User cancelled')) {
                                    showNotification('Login cancelled by user.', 'info', 3000);
                                } else {
                                    showNotification('Login failed. Please try again or contact support.', 'error', 5000);
                                }
                            });
                    });
            }

        
            // User filter widget removed for simplified interface
            
            // Function to add clustering toggle for large datasets
            function addClusteringToggle(layer, featureCount) {
                const toggleButton = document.createElement('calcite-button');
                toggleButton.innerHTML = '🔗 Toggle Clustering';
                toggleButton.kind = 'outline';
                toggleButton.scale = 's';
                toggleButton.title = `Toggle clustering for ${featureCount} features`;
                
                let clusteringEnabled = true;
                
                toggleButton.addEventListener('click', () => {
                    if (clusteringEnabled) {
                        // Disable clustering - show actual geometries
                        layer.featureReduction = null;
                        toggleButton.innerHTML = '🔗 Enable Clustering';
                        toggleButton.kind = 'neutral';
                        showNotification('Clustering disabled - showing actual feature shapes', 'info', 2000);
                        clusteringEnabled = false;
                    } else {
                        // Enable clustering
                        layer.featureReduction = {
                            type: "cluster",
                            clusterRadius: featureCount > 5000 ? "150px" : "100px",
                            clusterMinSize: "24px",
                            clusterMaxSize: "60px",
                            labelingInfo: [{
                                deconflictionStrategy: "none",
                                labelExpressionInfo: {
                                    expression: "Text($feature.cluster_count, '#,###')"
                                },
                                symbol: {
                                    type: "text",
                                    color: "#004c73",
                                    font: {
                                        weight: "bold",
                                        family: "Noto Sans",
                                        size: "12px"
                                    }
                                },
                                labelPlacement: "center-center"
                            }]
                        };
                        toggleButton.innerHTML = '🔗 Disable Clustering';
                        toggleButton.kind = 'outline';
                        showNotification('Clustering enabled - features grouped for performance', 'info', 2000);
                        clusteringEnabled = true;
                    }
                });
                
                // Add button to map UI
                view.ui.add(toggleButton, "top-right");
            }
            
                // Set global reference to feature layer for filtering
                globalFeatureLayer = featureLayer;
                // Store view reference for quick actions
                if (featureLayer) {
                    featureLayer.view = view;
                }

                // Watch for when the feature layer view is ready
            view.whenLayerView(featureLayer).then((layerView) => {
                    globalFeatureLayerView = layerView;
                    
                    // Populate filter dropdowns when layer is ready
                    window.populateFilterDropdowns();
                    
                    console.log('Feature layer view ready for filtering');
                });

            }); // End of require callback

        } // End of initializeArcGISComponents function

        // Global configuration access function (accessible from HTML onclick)
        window.openConfiguration = function() {
            // Get current user from the application context
            const configButton = document.getElementById('config-button');
            if (!configButton || configButton.style.display === 'none') {
                console.log('🔧 Configuration access denied - button not visible');
                console.log('Current user:', currentUser);
                console.log('Authorized users:', AUTHORIZED_CONFIG_USERS);
                
                if (currentUser) {
                    showNotification(`Configuration access not available for user: ${currentUser}`, 'warning', 3000);
                } else {
                    showNotification('Please sign in to access configuration', 'warning', 3000);
                }
                return;
            }
            
            // If button is visible, user is authorized
            console.log('✅ Opening configuration for user:', currentUser);
            window.open('config.html', '_blank');
        };
        
        // Global function to manually trigger auth state check (for debugging)
        window.debugAuthState = function() {
            console.log('🔍 Manual authentication state debug:');
            console.log('Current user:', currentUser);
            console.log('Authorized users:', AUTHORIZED_CONFIG_USERS);
            
            const signInButton = document.getElementById("sign-in-button");
            const navigationUser = document.getElementById("nav-user");
            const configButton = document.getElementById('config-button');
            
            console.log('UI Elements state:', {
                signInButton: signInButton ? { hidden: signInButton.hidden, display: signInButton.style.display } : 'not found',
                navigationUser: navigationUser ? { hidden: navigationUser.hidden, display: navigationUser.style.display, username: navigationUser.username } : 'not found',
                configButton: configButton ? { display: configButton.style.display, title: configButton.title } : 'not found'
            });
            
            // Check localStorage
            const storedUser = localStorage.getItem('erdb_authenticated_user');
            const storedTimestamp = localStorage.getItem('erdb_auth_timestamp');
            console.log('LocalStorage auth:', { user: storedUser, timestamp: storedTimestamp });
            
            // Force auth state recovery
            if (storedUser && !currentUser) {
                console.log('🔧 Forcing authentication state recovery...');
                forceAuthenticationStateRecovery();
            }
        };

        // Global Calcite notification function (accessible from config access)
        window.showNotification = function(message, type = 'info', duration = 5000) {
            // Create notification container if it doesn't exist
            let container = document.querySelector('.calcite-notification-container');
            if (!container) {
                container = document.createElement('div');
                container.className = 'calcite-notification-container';
                document.body.appendChild(container);
            }

            // Map types to Calcite notice kinds
            const kindMap = {
                'info': 'brand',
                'success': 'success', 
                'warning': 'warning',
                'error': 'danger'
            };

            // Map types to icons
            const iconMap = {
                'info': 'information',
                'success': 'check-circle',
                'warning': 'exclamation-mark-triangle',
                'error': 'exclamation-mark-triangle-f'
            };

            // Create Calcite notice
            const notice = document.createElement('calcite-notice');
            notice.setAttribute('open', '');
            notice.setAttribute('kind', kindMap[type] || 'brand');
            notice.setAttribute('icon', iconMap[type] || 'information');
            notice.setAttribute('auto-close', '');
            notice.setAttribute('auto-close-duration', duration.toString());

            // Create notice message
            const noticeMessage = document.createElement('div');
            noticeMessage.setAttribute('slot', 'message');
            // Simple sanitization for global function
            const sanitizedMessage = String(message).replace(/[<>\"'&]/g, '');
            noticeMessage.textContent = sanitizedMessage;
            notice.appendChild(noticeMessage);

            container.appendChild(notice);

            // Auto-remove from DOM after animation
            setTimeout(() => {
                if (notice.parentNode) {
                    notice.remove();
                }
            }, duration + 500);
            
            return notice;
        };

        // Global variables for filtering
        let globalFeatureLayerView = null;
        let globalFeatureLayer = null;
        let globalFilterValues = {
            office_level_2: '',
            region: ''
        };

        // Global function to populate filter dropdowns with unique values
        window.populateFilterDropdowns = function() {
            if (!globalFeatureLayer) return;

            globalFeatureLayer.queryFeatures().then((results) => {
                const features = results.features;
                
                // Get unique values for Office Level 2
                const officeLevels = [...new Set(features
                    .map(f => f.attributes.office_level_2)
                    .filter(val => val && val.trim() !== '')
                )].sort();

                // Get unique values for Region
                const regions = [...new Set(features
                    .map(f => f.attributes.region)
                    .filter(val => val && val.trim() !== '')
                )].sort();

                // Populate Office Level 2 dropdown
                const officeLevelSelect = document.getElementById('office-level-2-filter');
                if (officeLevelSelect) {
                    officeLevelSelect.innerHTML = '<calcite-option value="">All Office Levels</calcite-option>';
                    officeLevels.forEach(level => {
                        const option = document.createElement('calcite-option');
                        option.value = level;
                        option.textContent = level;
                        officeLevelSelect.appendChild(option);
                    });
                }

                // Populate Region dropdown
                const regionSelect = document.getElementById('region-filter');
                if (regionSelect) {
                    regionSelect.innerHTML = '<calcite-option value="">All Regions</calcite-option>';
                    regions.forEach(region => {
                        const option = document.createElement('calcite-option');
                        option.value = region;
                        option.textContent = region;
                        regionSelect.appendChild(option);
                    });
                }

                console.log('Filter dropdowns populated:', {
                    officeLevels: officeLevels.length,
                    regions: regions.length
                });
            }).catch((error) => {
                console.error('Error populating filter dropdowns:', error);
            });
        };

        // Global filter functions (accessible from HTML onclick)
        window.toggleFilterPanel = function() {
            const filterShellPanel = document.getElementById('filter-shell-panel');
            if (filterShellPanel) {
                const isCollapsed = filterShellPanel.hasAttribute('collapsed');
                
                if (isCollapsed) {
                    filterShellPanel.removeAttribute('collapsed');
                    // Panel is being opened, update filter status
                    window.updateFilterStatus();
                } else {
                    filterShellPanel.setAttribute('collapsed', '');
                }
            }
        };

        window.applyFilters = function() {
            const officeLevelSelect = document.getElementById('office-level-2-filter');
            const regionSelect = document.getElementById('region-filter');
            
            if (officeLevelSelect && regionSelect) {
                globalFilterValues.office_level_2 = officeLevelSelect.value;
                globalFilterValues.region = regionSelect.value;
                
                window.applyLayerFilters();
                
                showNotification('Filters applied successfully!', 'success', 2000);
            }
        };

        window.clearFilters = function() {
            const officeLevelSelect = document.getElementById('office-level-2-filter');
            const regionSelect = document.getElementById('region-filter');
            
            if (officeLevelSelect && regionSelect) {
                officeLevelSelect.value = '';
                regionSelect.value = '';
                globalFilterValues.office_level_2 = '';
                globalFilterValues.region = '';
                
                window.applyLayerFilters();
                
                showNotification('Filters cleared - showing all buildings', 'info', 2000);
            }
        };

        // Global filter utility functions
        window.applyLayerFilters = function() {
            if (!globalFeatureLayerView) return;

            let whereClause = "1=1"; // Default to show all
            const conditions = [];

            if (globalFilterValues.office_level_2) {
                conditions.push(`office_level_2 = '${globalFilterValues.office_level_2.replace(/'/g, "''")}'`);
            }

            if (globalFilterValues.region) {
                conditions.push(`region = '${globalFilterValues.region.replace(/'/g, "''")}'`);
            }

            if (conditions.length > 0) {
                whereClause = conditions.join(' AND ');
            }

            globalFeatureLayerView.filter = {
                where: whereClause
            };

            // Update status
            window.updateFilterStatus();
            console.log('Filter applied:', whereClause);
        };

        window.updateFilterStatus = function() {
            const statusElement = document.getElementById('filter-status');
            const statusNotice = document.getElementById('filter-status-notice');
            const filterStatusChip = document.getElementById('filter-status-chip');
            
            if (!statusElement || !statusNotice) return;

            if (globalFilterValues.office_level_2 || globalFilterValues.region) {
                const activeFilters = [];
                if (globalFilterValues.office_level_2) activeFilters.push(`Office: ${globalFilterValues.office_level_2}`);
                if (globalFilterValues.region) activeFilters.push(`Region: ${globalFilterValues.region}`);
                
                const filterText = `Active filters: ${activeFilters.join(', ')}`;
                statusElement.textContent = filterText;
                statusNotice.setAttribute('kind', 'success');
                statusNotice.setAttribute('icon', 'check-circle');
                
                // Update status chip
                if (filterStatusChip) {
                    filterStatusChip.innerHTML = `<calcite-icon icon="filter" slot="icon"></calcite-icon>Filtered`;
                    filterStatusChip.setAttribute('kind', 'brand');
                }
            } else {
                statusElement.textContent = 'No filters active - showing all buildings';
                statusNotice.setAttribute('kind', 'info');
                statusNotice.setAttribute('icon', 'information');
                
                // Update status chip
                if (filterStatusChip) {
                    filterStatusChip.innerHTML = `<calcite-icon icon="filter" slot="icon"></calcite-icon>No filters`;
                    filterStatusChip.setAttribute('kind', 'neutral');
                }
            }
        };

        // Enhanced layer status update function
        window.updateLayerStatus = function(featureCount, layerName) {
            const statusCard = document.getElementById('layer-status-card');
            const statusSubtitle = document.getElementById('layer-status-subtitle');
            const featureCountChip = document.getElementById('feature-count-chip');
            
            if (statusCard && statusSubtitle && featureCountChip) {
                statusSubtitle.textContent = layerName || 'DENR Administrative Buildings';
                featureCountChip.innerHTML = `<calcite-icon icon="number" slot="icon"></calcite-icon>Features: ${featureCount || 0}`;
                
                // Show the status card
                statusCard.style.display = 'block';
                
                // Auto-hide after 5 seconds unless there are active filters
                if (!globalFilterValues.office_level_2 && !globalFilterValues.region) {
                    setTimeout(() => {
                        if (statusCard.style.display !== 'none') {
                            statusCard.style.display = 'none';
                        }
                    }, 5000);
                }
            }
        };

        // Enhanced Quick Action Functions
        window.zoomToAllBuildings = function() {
            if (globalFeatureLayer && globalFeatureLayer.fullExtent) {
                const view = globalFeatureLayer.view;
                if (view) {
                    view.goTo(globalFeatureLayer.fullExtent.expand(1.2)).then(() => {
                        showNotification('Zoomed to show all buildings', 'success', 2000);
                    });
                }
            }
        };

        window.refreshLayerData = function() {
            if (globalFeatureLayer) {
                const loadingOverlay = showLoadingOverlay(document.getElementById('mapContainer'), 'Refreshing building data...');
                
                globalFeatureLayer.refresh().then(() => {
                    hideLoadingOverlay(loadingOverlay);
                    showNotification('Building data refreshed successfully', 'success', 3000);
                    
                    // Repopulate filter dropdowns with fresh data
                    window.populateFilterDropdowns();
                }).catch((error) => {
                    hideLoadingOverlay(loadingOverlay);
                    showNotification('Failed to refresh data: ' + error.message, 'error', 5000);
                });
            }
        };

        window.exportVisibleFeatures = function() {
            showNotification('Export feature coming soon!', 'info', 3000);
        };

        // Enhanced FAB interaction
        window.addEventListener('DOMContentLoaded', function() {
            const fab = document.getElementById('quick-actions-fab');
            const popover = document.getElementById('quick-actions-popover');
            
            if (fab && popover) {
                fab.addEventListener('click', () => {
                    popover.open = !popover.open;
                });
            }
        });

        // Force authentication state recovery on page load
        function forceAuthenticationStateRecovery() {
            console.log('🔄 Force authentication state recovery...');
            
            // Check localStorage for stored authentication
            const storedUser = localStorage.getItem('erdb_authenticated_user');
            const storedTimestamp = localStorage.getItem('erdb_auth_timestamp');
            
            if (storedUser && storedTimestamp) {
                // Check if auth is still valid (within 24 hours)
                const timeDiff = Date.now() - parseInt(storedTimestamp);
                const isValid = timeDiff < (24 * 60 * 60 * 1000);
                
                if (isValid) {
                    console.log(`🔍 Found valid stored auth for user: ${storedUser}`);
                    
                    // Force UI update with stored auth
                    const userInfo = {
                        username: storedUser,
                        fullName: storedUser, // Fallback if full name not available
                        email: null
                    };
                    
                    currentUser = storedUser;
                    updateUIAuthenticationState(true, userInfo);
                    console.log('✅ Authentication UI state recovered from localStorage');
                } else {
                    console.log('⚠️ Stored authentication expired, clearing...');
                    localStorage.removeItem('erdb_authenticated_user');
                    localStorage.removeItem('erdb_auth_timestamp');
                }
            } else {
                console.log('ℹ️ No stored authentication found');
            }
        }
        
        // Enhanced application initialization with auth state recovery
        function initializeWithAuthRecovery() {
            console.log('🚀 Initializing application with auth recovery...');
            
            // First, try to recover authentication state from localStorage
            forceAuthenticationStateRecovery();
            
            // Then proceed with normal initialization
            
        // Check if modules are bundled, otherwise load them dynamically
        if (!window.ERDB_BUNDLED_MODE && typeof ModuleLoader !== 'undefined') {
            ModuleLoader.initializeApp(['security', 'app-config'], initializeApplication);
        } else {
            // Bundled mode - proceed directly
            initializeApplication();
        }
            // Add a delayed auth state validation after everything loads
            setTimeout(() => {
                console.log('🔍 Post-initialization auth state validation...');
                
                // If we still don't have proper UI state, try to force it
                const signInButton = document.getElementById("sign-in-button");
                const navigationUser = document.getElementById("nav-user");
                const configButton = document.getElementById('config-button');
                
                console.log('🔍 Current UI elements state:', {
                    signInButton: signInButton ? !signInButton.hidden : 'not found',
                    navigationUser: navigationUser ? !navigationUser.hidden : 'not found',
                    configButton: configButton ? configButton.style.display : 'not found',
                    currentUser: currentUser
                });
                
                // If we have a currentUser but UI doesn't reflect it, fix it
                if (currentUser && signInButton && !signInButton.hidden) {
                    console.log('🔧 Fixing authentication UI state mismatch...');
                    const userInfo = {
                        username: currentUser,
                        fullName: currentUser,
                        email: null
                    };
                    updateUIAuthenticationState(true, userInfo);
                }
            }, 2000); // Wait 2 seconds for everything to initialize
        }

        // Initialize application when DOM is ready - using modular approach with auth recovery
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                // Check if modules are bundled, otherwise load them dynamically
                if (!window.ERDB_BUNDLED_MODE && typeof ModuleLoader !== 'undefined') {
                    ModuleLoader.initializeApp(['security', 'app-config', 'error-manager', 'auth-manager'], initializeWithAuthRecovery);
                } else {
                    // Bundled mode or modules already loaded - proceed directly
                    initializeWithAuthRecovery();
                }
            });
        } else {
            // DOM is already ready, initialize immediately
            if (!window.ERDB_BUNDLED_MODE && typeof ModuleLoader !== 'undefined') {
                ModuleLoader.initializeApp(['security', 'app-config', 'error-manager', 'auth-manager'], initializeWithAuthRecovery);
            } else {
            initializeWithAuthRecovery();
            }
        }

    </script>

    <script>
        /* 
         * ERDB Control Map - Bundled Modules
         * Generated on: 2025-09-17T06:52:49.115Z
         * Modules included: security, app-config, module-loader, error-manager, auth-manager
         */
        
        // Module bundling flag
        window.ERDB_BUNDLED_MODE = true;
        
        
        // ==================== SECURITY MODULE ====================
        const SecurityUtils = {
    /**
     * Encrypt sensitive data using Web Crypto API
     * @param {string} data - Data to encrypt
     * @param {string} password - Password for encryption
     * @returns {Promise<Object>} Encrypted data object
     */
    async encryptData(data, password) {
        try {
            const encoder = new TextEncoder();
            const salt = crypto.getRandomValues(new Uint8Array(16));
            const iv = crypto.getRandomValues(new Uint8Array(12));
            
            const key = await crypto.subtle.importKey(
                'raw',
                encoder.encode(password),
                { name: 'PBKDF2' },
                false,
                ['deriveKey']
            );
            
            const derivedKey = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: salt,
                    iterations: 100000,
                    hash: 'SHA-256'
                },
                key,
                { name: 'AES-GCM', length: 256 },
                false,
                ['encrypt']
            );
            
            const encrypted = await crypto.subtle.encrypt(
                { name: 'AES-GCM', iv: iv },
                derivedKey,
                encoder.encode(data)
            );
            
            return {
                encrypted: Array.from(new Uint8Array(encrypted)),
                salt: Array.from(salt),
                iv: Array.from(iv)
            };
        } catch (error) {
            console.error('Encryption error:', error);
            throw new Error('Failed to encrypt data');
        }
    },
    
    /**
     * Decrypt sensitive data
     * @param {Object} encryptedData - Encrypted data object
     * @param {string} password - Password for decryption
     * @returns {Promise<string>} Decrypted data
     */
    async decryptData(encryptedData, password) {
        try {
            const encoder = new TextEncoder();
            const decoder = new TextDecoder();
            
            const key = await crypto.subtle.importKey(
                'raw',
                encoder.encode(password),
                { name: 'PBKDF2' },
                false,
                ['deriveKey']
            );
            
            const derivedKey = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: new Uint8Array(encryptedData.salt),
                    iterations: 100000,
                    hash: 'SHA-256'
                },
                key,
                { name: 'AES-GCM', length: 256 },
                false,
                ['decrypt']
            );
            
            const decrypted = await crypto.subtle.decrypt(
                { name: 'AES-GCM', iv: new Uint8Array(encryptedData.iv) },
                derivedKey,
                new Uint8Array(encryptedData.encrypted)
            );
            
            return decoder.decode(decrypted);
        } catch (error) {
            console.error('Decryption error:', error);
            throw new Error('Failed to decrypt data');
        }
    },
    
    /**
     * Input validation and sanitization utilities
     */
    validateAndSanitize: {
        /**
         * Validate and sanitize URL input
         * @param {string} input - URL to validate
         * @returns {string|null} Valid URL or null
         */
        url(input) {
            if (!input || typeof input !== 'string') return null;
            try {
                const url = new URL(input.trim());
                return ['http:', 'https:'].includes(url.protocol) ? url.href : null;
            } catch {
                return null;
            }
        },
        
        /**
         * Sanitize text input
         * @param {string} input - Text to sanitize
         * @param {number} maxLength - Maximum length
         * @returns {string} Sanitized text
         */
        text(input, maxLength = 1000) {
            if (!input || typeof input !== 'string') return '';
            return input.trim().substring(0, maxLength).replace(/[<>"'&]/g, '');
        },
        
        /**
         * Validate and sanitize numeric input
         * @param {any} input - Number to validate
         * @param {number} min - Minimum value
         * @param {number} max - Maximum value
         * @returns {number|null} Valid number or null
         */
        number(input, min = -Infinity, max = Infinity) {
            const num = parseFloat(input);
            return isNaN(num) ? null : Math.max(min, Math.min(max, num));
        },
        
        /**
         * Validate and sanitize coordinate input
         * @param {any} input - Coordinates to validate
         * @returns {Array|null} Valid coordinates or null
         */
        coordinates(input) {
            try {
                const coords = Array.isArray(input) ? input : JSON.parse(input);
                if (!Array.isArray(coords) || coords.length !== 2) return null;
                const [lng, lat] = coords.map(c => parseFloat(c));
                if (isNaN(lng) || isNaN(lat)) return null;
                return [Math.max(-180, Math.min(180, lng)), Math.max(-90, Math.min(90, lat))];
            } catch {
                return null;
            }
        }
    }
};
        
        
        // ==================== APP-CONFIG MODULE ====================
        const AppConfig = {
    // Default configuration
    default: {
        app: {
            title: "DENR Control Map",
            description: "Administrative Buildings Control System",
            logoHeading: "DENR Infrastructure Map",
            signOutDescription: "Administrative Buildings Control System. To sign out, click on the logged in user button.",
            signInDescription: "Sign in to DENR Portal to access the administrative buildings database."
        },
        map: {
            center: [122, 12],
            zoom: 6,
            basemap: "topo",
            alternateBasemap: "satellite"
        },
        services: {
            portalUrl: "https://controlmap.denr.gov.ph/arcgis",
            featureLayerUrl: "https://controlmap.denr.gov.ph/server/rest/services/Hosted/survey123_698ecf476c9640b6a70fc48da500e50a_results/FeatureServer"
        },
        authentication: {
            appId: "5ybGb7GnJFt6V7Rd",
            requireAuth: false,
            authMode: "oauth", // "oauth", "token", "credentials"
            username: "",
            password: "",
            token: "",
            tokenExpiration: null,
            autoSignIn: false
        },
        layout: {
            mapHeight: "70%",
            tableHeight: "30%"
        },
        popupTemplate: {
            title: "{name_building}",
            fieldInfos: [
                // Administrative Information
                { fieldName: "office_level_1", label: "Office Level 1" },
                { fieldName: "office_level_2", label: "Office Level 2" },
                { fieldName: "region", label: "Region" },
                { fieldName: "bureau", label: "Bureau" },
                { fieldName: "penro", label: "PENRO" },
                { fieldName: "cenro", label: "CENRO" },
                
                // Building Information
                { fieldName: "name_building", label: "Building Name" },
                { fieldName: "facility_code", label: "Facility Code" },
                { fieldName: "facility_id", label: "Facility ID" },
                { fieldName: "building_purpose", label: "Building Purpose" },
                { fieldName: "building_purpose_others", label: "Other Purpose" },
                { fieldName: "year_constructed", label: "Year Constructed" },
                { fieldName: "floor_area", label: "Floor Area (sq.m)", format: { digitSeparator: true, places: 2 } },
                { fieldName: "number_floor", label: "Number of Floors" },
                { fieldName: "max_occupants", label: "Max Occupants", format: { digitSeparator: true } },
                { fieldName: "buildingvalue", label: "Building Value", format: { digitSeparator: true, places: 2 } },
                
                // Land Information
                { fieldName: "street_address", label: "Street Address" },
                { fieldName: "land_area", label: "Land Area (sq.m)", format: { digitSeparator: true, places: 2 } },
                { fieldName: "registered_owner", label: "Registered Owner" },
                { fieldName: "ownership_type", label: "Ownership Type" },
                { fieldName: "year_acquired", label: "Year Acquired" },
                
                // Data Entry Information
                { fieldName: "name_encoder", label: "Encoder" },
                { fieldName: "email", label: "Email" },
                { fieldName: "remarks", label: "Remarks" }
            ]
        },
        tableTemplate: {
            columnTemplates: [
                // Key Building Information (most important columns first)
                { type: "field", fieldName: "name_building", label: "Building Name" },
                { type: "field", fieldName: "facility_code", label: "Facility Code" },
                { type: "field", fieldName: "building_purpose", label: "Building Purpose" },
                { type: "field", fieldName: "region", label: "Region" },
                { type: "field", fieldName: "bureau", label: "Bureau" },
                { type: "field", fieldName: "office_level_1", label: "Office Level 1" },
                { type: "field", fieldName: "office_level_2", label: "Office Level 2" },
                
                // Physical Information
                { type: "field", fieldName: "street_address", label: "Street Address" },
                { type: "field", fieldName: "floor_area", label: "Floor Area (sq.m)" },
                { type: "field", fieldName: "land_area", label: "Land Area (sq.m)" },
                { type: "field", fieldName: "number_floor", label: "Floors" },
                { type: "field", fieldName: "max_occupants", label: "Max Occupants" },
                { type: "field", fieldName: "year_constructed", label: "Year Built" },
                { type: "field", fieldName: "buildingvalue", label: "Building Value" },
                
                // Ownership & Administrative
                { type: "field", fieldName: "registered_owner", label: "Owner" },
                { type: "field", fieldName: "ownership_type", label: "Ownership Type" },
                { type: "field", fieldName: "year_acquired", label: "Year Acquired" },
                { type: "field", fieldName: "penro", label: "PENRO" },
                { type: "field", fieldName: "cenro", label: "CENRO" },
                
                // Additional Information
                { type: "field", fieldName: "facility_id", label: "Facility ID" },
                { type: "field", fieldName: "building_purpose_others", label: "Other Purpose" },
                { type: "field", fieldName: "name_encoder", label: "Encoder" },
                { type: "field", fieldName: "email", label: "Email" },
                { type: "field", fieldName: "remarks", label: "Remarks" }
            ]
        }
    },

    /**
     * Load configuration from localStorage or use defaults
     * @returns {Object} Configuration object
     */
    load() {
        try {
            const stored = localStorage.getItem('erdb-config');
            if (stored) {
                const config = JSON.parse(stored);
                
                // Auto-fix problematic portal URL with /home suffix
                if (config.services && config.services.portalUrl && 
                    config.services.portalUrl.includes('/home')) {
                    console.warn('Detected problematic portal URL with /home suffix, auto-correcting...');
                    config.services.portalUrl = config.services.portalUrl.replace('/home', '');
                    localStorage.setItem('erdb-config', JSON.stringify(config));
                    console.log('Portal URL auto-corrected to:', config.services.portalUrl);
                }
                
                // Auto-fix MapServer URLs to FeatureServer
                if (config.services && config.services.featureLayerUrl && 
                    config.services.featureLayerUrl.includes('MapServer')) {
                    console.warn('Detected MapServer URL, auto-correcting to FeatureServer...');
                    config.services.featureLayerUrl = config.services.featureLayerUrl.replace('MapServer', 'FeatureServer');
                    localStorage.setItem('erdb-config', JSON.stringify(config));
                    console.log('Feature Layer URL auto-corrected to:', config.services.featureLayerUrl);
                }
                
                console.log('Configuration loaded from localStorage');
                return { ...this.default, ...config };
            }
        } catch (error) {
            console.warn('Error loading stored configuration:', error.message);
        }
        console.log('Using default configuration');
        return { ...this.default };
    },

    /**
     * Save configuration to localStorage with security enhancements
     * @param {Object} config - Configuration to save
     * @returns {Promise<boolean>} Success status
     */
    async save(config) {
        try {
            // Validate configuration before saving
            const validatedConfig = this.validateConfiguration(config);
            
            // Encrypt sensitive authentication data if SecurityUtils is available
            if (typeof SecurityUtils !== 'undefined' && validatedConfig.authentication && validatedConfig.authentication.password) {
                const deviceId = await this.getDeviceId();
                const encryptedPassword = await SecurityUtils.encryptData(
                    validatedConfig.authentication.password, 
                    deviceId
                );
                validatedConfig.authentication.password = encryptedPassword;
                validatedConfig.authentication.encrypted = true;
            }
            
            localStorage.setItem('erdb-config', JSON.stringify(validatedConfig));
            console.log('Configuration saved securely to localStorage');
            return true;
        } catch (error) {
            console.error('Error saving configuration:', error.message);
            return false;
        }
    },

    /**
     * Validate configuration object
     * @param {Object} config - Configuration to validate
     * @returns {Object} Validated configuration
     */
    validateConfiguration(config) {
        const validated = { ...config };
        
        // Use SecurityUtils if available, otherwise basic validation
        const sanitizer = typeof SecurityUtils !== 'undefined' ? SecurityUtils.validateAndSanitize : {
            text: (input, maxLength = 1000) => input ? String(input).trim().substring(0, maxLength) : '',
            url: (input) => {
                try {
                    const url = new URL(input);
                    return ['http:', 'https:'].includes(url.protocol) ? url.href : null;
                } catch {
                    return null;
                }
            },
            number: (input, min = -Infinity, max = Infinity) => {
                const num = parseFloat(input);
                return isNaN(num) ? null : Math.max(min, Math.min(max, num));
            },
            coordinates: (input) => {
                try {
                    const coords = Array.isArray(input) ? input : JSON.parse(input);
                    if (!Array.isArray(coords) || coords.length !== 2) return null;
                    const [lng, lat] = coords.map(c => parseFloat(c));
                    if (isNaN(lng) || isNaN(lat)) return null;
                    return [Math.max(-180, Math.min(180, lng)), Math.max(-90, Math.min(90, lat))];
                } catch {
                    return null;
                }
            }
        };
        
        // Validate app settings
        if (validated.app) {
            validated.app.title = sanitizer.text(validated.app.title, 100);
            validated.app.description = sanitizer.text(validated.app.description, 500);
        }
        
        // Validate map settings
        if (validated.map) {
            validated.map.center = sanitizer.coordinates(validated.map.center) || [122, 12];
            validated.map.zoom = sanitizer.number(validated.map.zoom, 1, 20) || 6;
        }
        
        // Validate service URLs
        if (validated.services) {
            validated.services.portalUrl = sanitizer.url(validated.services.portalUrl);
            validated.services.featureLayerUrl = sanitizer.url(validated.services.featureLayerUrl);
        }
        
        // Validate authentication settings
        if (validated.authentication) {
            validated.authentication.appId = sanitizer.text(validated.authentication.appId, 100);
            validated.authentication.username = sanitizer.text(validated.authentication.username, 100);
        }
        
        return validated;
    },

    /**
     * Get device-specific identifier for encryption
     * @returns {Promise<string>} Device ID
     */
    async getDeviceId() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Device fingerprint', 2, 2);
            
            const fingerprint = canvas.toDataURL() + 
                             navigator.userAgent + 
                             navigator.language + 
                             screen.width + 'x' + screen.height;
            
            const encoder = new TextEncoder();
            const data = encoder.encode(fingerprint);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        } catch (error) {
            console.warn('Could not generate device ID:', error);
            return 'default-device-id';
        }
    },

    /**
     * Reset to defaults
     * @returns {boolean} Success status
     */
    reset() {
        try {
            localStorage.removeItem('erdb-config');
            console.log('Configuration reset to defaults');
            return true;
        } catch (error) {
            console.error('Error resetting configuration:', error.message);
            return false;
        }
    },

    /**
     * Apply URL parameter overrides
     * @param {Object} config - Base configuration
     * @returns {Object} Configuration with URL overrides
     */
    applyUrlOverrides(config) {
        const urlParams = new URLSearchParams(window.location.search);
        
        try {
            if (urlParams.get('portalUrl')) {
                config.services.portalUrl = urlParams.get('portalUrl');
                console.log('Override: portalUrl =', config.services.portalUrl);
            }
            if (urlParams.get('featureLayerUrl')) {
                config.services.featureLayerUrl = urlParams.get('featureLayerUrl');
                console.log('Override: featureLayerUrl =', config.services.featureLayerUrl);
            }
            if (urlParams.get('basemap')) {
                config.map.basemap = urlParams.get('basemap');
                console.log('Override: basemap =', config.map.basemap);
            }
            if (urlParams.get('center')) {
                config.map.center = JSON.parse(urlParams.get('center'));
                console.log('Override: center =', config.map.center);
            }
            if (urlParams.get('zoom')) {
                config.map.zoom = parseInt(urlParams.get('zoom'));
                console.log('Override: zoom =', config.map.zoom);
            }
            if (urlParams.get('appId')) {
                config.authentication.appId = urlParams.get('appId');
                console.log('Override: appId =', config.authentication.appId);
            }
        } catch (paramError) {
            console.warn('Error processing URL parameters:', paramError.message);
        }
        
        return config;
    },

    /**
     * Auto-discover fields from feature layer URL
     * @param {string} layerUrl - Feature layer URL
     * @param {string} authToken - Optional authentication token
     * @returns {Promise<Object>} Discovered configuration
     */
    async discoverFields(layerUrl, authToken = null) {
        try {
            console.log('Discovering fields from:', layerUrl);
            
            let serviceUrl = layerUrl;
            if (!serviceUrl.endsWith('/')) {
                serviceUrl += '/';
            }
            
            const urlParams = new URLSearchParams();
            urlParams.append('f', 'json');
            
            if (authToken) {
                urlParams.append('token', authToken);
                console.log('Using authentication token for field discovery');
            }
            
            serviceUrl += '?' + urlParams.toString();

            const response = await fetch(serviceUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    throw new Error('Authentication required. Please check your credentials or ensure the service is publicly accessible.');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const layerInfo = await response.json();
            
            if (layerInfo.error) {
                throw new Error(layerInfo.error.message || 'Service returned an error');
            }

            if (!layerInfo.fields || !Array.isArray(layerInfo.fields)) {
                throw new Error('No field information found in service response');
            }

            // Filter out system fields and create configurations
            const systemFields = ['OBJECTID', 'SHAPE', 'SHAPE_Length', 'SHAPE_Area', 'GlobalID', 'created_user', 'created_date', 'last_edited_user', 'last_edited_date'];
            const userFields = layerInfo.fields.filter(field => 
                !systemFields.includes(field.name) && 
                !field.name.startsWith('SHAPE') &&
                field.type !== 'esriFieldTypeGeometry' &&
                field.type !== 'esriFieldTypeOID'
            );

            const tableColumns = userFields.map(field => ({
                type: "field",
                fieldName: field.name,
                label: field.alias || field.name,
                direction: "asc"
            }));

            const popupFields = userFields.map(field => ({
                fieldName: field.name,
                label: field.alias || field.name,
                visible: true
            }));

            const titleField = layerInfo.displayField || 
                             (popupFields.length > 0 ? popupFields[0].fieldName : 'OBJECTID');

            const discoveredConfig = {
                tableTemplate: {
                    columnTemplates: tableColumns
                },
                popupTemplate: {
                    title: `{${titleField}}`,
                    fieldInfos: popupFields
                },
                layerInfo: {
                    name: layerInfo.name || 'Feature Layer',
                    description: layerInfo.description || '',
                    geometryType: layerInfo.geometryType || '',
                    fields: layerInfo.fields,
                    capabilities: layerInfo.capabilities || ''
                }
            };

            console.log('Field discovery successful:', discoveredConfig);
            return discoveredConfig;

        } catch (error) {
            console.error('Error discovering fields:', error);
            throw error;
        }
    }
};
        
        
        // ==================== MODULE-LOADER MODULE ====================
        const ModuleLoader = {
    loadedModules: new Set(),
    dependencies: {
        'security': [],
        'app-config': ['security'],
        'ui-manager': ['security', 'app-config'],
        'auth-manager': ['security', 'app-config'],
        'map-manager': ['app-config']
    },

    /**
     * Load a module with its dependencies
     * @param {string} moduleName - Name of the module to load
     * @returns {Promise<void>} Promise that resolves when module is loaded
     */
    async loadModule(moduleName) {
        if (this.loadedModules.has(moduleName)) {
            return Promise.resolve();
        }

        // Load dependencies first
        const deps = this.dependencies[moduleName] || [];
        for (const dep of deps) {
            await this.loadModule(dep);
        }

        // Check if module is already available (bundled mode)
        if (this.isModuleAvailable(moduleName)) {
            this.loadedModules.add(moduleName);
            return Promise.resolve();
        }

        // Try to load from separate file (development mode)
        try {
            await this.loadScript(`js/${moduleName}.js`);
            this.loadedModules.add(moduleName);
            console.log(`Module '${moduleName}' loaded successfully`);
        } catch (error) {
            console.warn(`Failed to load module '${moduleName}':`, error);
            throw new Error(`Module '${moduleName}' not found in bundled or separate file mode`);
        }
    },

    /**
     * Check if module is already available
     * @param {string} moduleName - Name of the module
     * @returns {boolean} True if module is available
     */
    isModuleAvailable(moduleName) {
        const moduleMap = {
            'security': () => typeof SecurityUtils !== 'undefined',
            'app-config': () => typeof AppConfig !== 'undefined',
            'ui-manager': () => typeof UIManager !== 'undefined',
            'auth-manager': () => typeof AuthManager !== 'undefined',
            'map-manager': () => typeof MapManager !== 'undefined'
        };

        return moduleMap[moduleName] ? moduleMap[moduleName]() : false;
    },

    /**
     * Load a script file
     * @param {string} src - Script source URL
     * @returns {Promise<void>} Promise that resolves when script is loaded
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            // Check if script is already loaded
            const existing = document.querySelector(`script[src="${src}"]`);
            if (existing) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            
            script.onload = () => resolve();
            script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
            
            document.head.appendChild(script);
        });
    },

    /**
     * Initialize application with required modules
     * @param {Array<string>} modules - List of required modules
     * @param {Function} callback - Callback to execute after loading
     * @returns {Promise<void>} Promise that resolves when initialization is complete
     */
    async initializeApp(modules, callback) {
        try {
            console.log('Loading application modules:', modules);
            
            // Load all required modules
            for (const module of modules) {
                await this.loadModule(module);
            }
            
            console.log('All modules loaded successfully');
            
            // Execute callback if provided
            if (typeof callback === 'function') {
                callback();
            }
            
        } catch (error) {
            console.error('Module loading failed:', error);
            
            // Show user-friendly error if notification system is available
            if (typeof window.showNotification === 'function') {
                window.showNotification(
                    'Application loading failed. Please refresh the page.', 
                    'error', 
                    10000
                );
            } else {
                alert('Application loading failed. Please refresh the page.');
            }
            
            throw error;
        }
    }
};

// Make available globally
if (typeof window !== 'undefined') {
    window.ModuleLoader = ModuleLoader;
}
        
        
        // ==================== ERROR-MANAGER MODULE ====================
        const ErrorManager = {
    errorLog: [],
    maxLogSize: 100,
    disposables: new Set(),
    memoryWatchers: new Map(),
    
    /**
     * Initialize error manager
     */
    init() {
        this.setupGlobalErrorHandling();
        this.setupMemoryMonitoring();
        this.setupUnloadHandlers();
        console.log('🛡️ Error Manager initialized');
    },

    /**
     * Setup global error handling
     */
    setupGlobalErrorHandling() {
        // Handle uncaught JavaScript errors
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                timestamp: new Date().toISOString()
            });
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logError({
                type: 'Unhandled Promise Rejection',
                message: event.reason?.message || String(event.reason),
                stack: event.reason?.stack,
                timestamp: new Date().toISOString()
            });
        });

        // Handle ArcGIS API errors if available
        if (typeof require !== 'undefined') {
            require(['esri/core/Error'], (EsriError) => {
                console.log('🗺️ ArcGIS error handling enabled');
            });
        }
    },

    /**
     * Setup memory monitoring
     */
    setupMemoryMonitoring() {
        // Monitor memory usage if Performance API is available
        if (window.performance && window.performance.memory) {
            setInterval(() => {
                const memory = window.performance.memory;
                const usage = {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                };

                // Warn if memory usage is high
                const usagePercent = (usage.used / usage.limit) * 100;
                if (usagePercent > 80) {
                    console.warn(`⚠️ High memory usage: ${usagePercent.toFixed(1)}%`);
                    this.suggestMemoryCleanup();
                }

                this.memoryWatchers.set('current', usage);
            }, 30000); // Check every 30 seconds
        }
    },

    /**
     * Setup page unload handlers for cleanup
     */
    setupUnloadHandlers() {
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        // Handle visibility change for mobile devices
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseNonCriticalOperations();
            } else {
                this.resumeOperations();
            }
        });
    },

    /**
     * Log error with details
     * @param {Object} errorInfo - Error information
     */
    logError(errorInfo) {
        // Add to internal log
        this.errorLog.push(errorInfo);
        
        // Trim log if it gets too large
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog = this.errorLog.slice(-this.maxLogSize);
        }

        // Console logging based on error type
        if (errorInfo.type === 'JavaScript Error') {
            console.error('🚨 JavaScript Error:', errorInfo.message, errorInfo);
        } else if (errorInfo.type === 'Unhandled Promise Rejection') {
            console.error('🚨 Promise Rejection:', errorInfo.message, errorInfo);
        } else {
            console.error('🚨 Application Error:', errorInfo);
        }

        // Show user notification for critical errors
        this.showUserErrorNotification(errorInfo);

        // Attempt recovery for known error types
        this.attemptErrorRecovery(errorInfo);
    },

    /**
     * Show user-friendly error notification
     * @param {Object} errorInfo - Error information
     */
    showUserErrorNotification(errorInfo) {
        // Only show notifications for errors that affect user experience
        const userRelevantErrors = [
            'feature layer',
            'authentication',
            'configuration',
            'portal',
            'network'
        ];

        const isUserRelevant = userRelevantErrors.some(keyword => 
            errorInfo.message.toLowerCase().includes(keyword)
        );

        if (isUserRelevant && typeof window.showNotification === 'function') {
            let userMessage = 'An error occurred. Please try again.';
            
            // Provide specific guidance based on error type
            if (errorInfo.message.includes('feature layer')) {
                userMessage = 'Unable to load map data. Please check your connection and refresh.';
            } else if (errorInfo.message.includes('authentication')) {
                userMessage = 'Authentication failed. Please try signing in again.';
            } else if (errorInfo.message.includes('network')) {
                userMessage = 'Network connection issue. Please check your internet connection.';
            }

            window.showNotification(userMessage, 'error', 8000);
        }
    },

    /**
     * Attempt automatic error recovery
     * @param {Object} errorInfo - Error information
     */
    attemptErrorRecovery(errorInfo) {
        // Recovery strategies for common errors
        if (errorInfo.message.includes('TypeError: Cannot read property') && 
            errorInfo.message.includes('null')) {
            console.log('🔧 Attempting DOM element recovery...');
            // Retry DOM operations after a short delay
            setTimeout(() => {
                if (typeof window.initializeApplication === 'function') {
                    console.log('🔄 Retrying application initialization...');
                }
            }, 1000);
        }

        if (errorInfo.message.includes('feature layer') || errorInfo.message.includes('FeatureServer')) {
            console.log('🔧 Attempting feature layer recovery...');
            // Try to reload feature layer with fallback URL
            setTimeout(() => {
                if (typeof window.reloadFeatureLayer === 'function') {
                    window.reloadFeatureLayer();
                }
            }, 2000);
        }
    },

    /**
     * Register a disposable resource for cleanup
     * @param {Object} resource - Resource to dispose
     * @param {Function} disposeFunc - Function to call for disposal
     */
    registerDisposable(resource, disposeFunc) {
        this.disposables.add({ resource, dispose: disposeFunc });
    },

    /**
     * Suggest memory cleanup
     */
    suggestMemoryCleanup() {
        console.log('🧹 Performing memory cleanup...');
        
        // Clear old error logs
        if (this.errorLog.length > 50) {
            this.errorLog = this.errorLog.slice(-50);
        }

        // Suggest garbage collection if available
        if (window.gc) {
            window.gc();
        }

        // Clean up cached data
        this.cleanupCachedData();
    },

    /**
     * Clean up cached data
     */
    cleanupCachedData() {
        // Clear old cached features if feature layer is available
        if (typeof window.globalFeatureLayer !== 'undefined' && 
            window.globalFeatureLayer?.queryFeatureCount) {
            console.log('🗑️ Clearing feature cache...');
        }

        // Clear old notifications
        const notifications = document.querySelectorAll('calcite-notice');
        notifications.forEach(notice => {
            if (notice.parentNode) {
                notice.remove();
            }
        });
    },

    /**
     * Pause non-critical operations
     */
    pauseNonCriticalOperations() {
        console.log('⏸️ Pausing non-critical operations (page hidden)');
        
        // Pause auto-refresh timers
        if (window.autoRefreshTimer) {
            clearInterval(window.autoRefreshTimer);
        }

        // Reduce map rendering frequency
        if (typeof window.globalFeatureLayer !== 'undefined') {
            // Reduce update frequency
        }
    },

    /**
     * Resume operations
     */
    resumeOperations() {
        console.log('▶️ Resuming operations (page visible)');
        
        // Resume timers and normal operation
        if (typeof window.restartAutoRefresh === 'function') {
            window.restartAutoRefresh();
        }
    },

    /**
     * Cleanup all resources
     */
    cleanup() {
        console.log('🧹 Cleaning up resources...');
        
        // Dispose all registered resources
        this.disposables.forEach(disposable => {
            try {
                disposable.dispose();
            } catch (error) {
                console.warn('Error disposing resource:', error);
            }
        });

        // Clear disposables set
        this.disposables.clear();

        // Clear memory watchers
        this.memoryWatchers.clear();

        // Clear error log
        this.errorLog = [];

        console.log('✅ Cleanup completed');
    },

    /**
     * Get error statistics
     * @returns {Object} Error statistics
     */
    getErrorStats() {
        const stats = {
            totalErrors: this.errorLog.length,
            errorTypes: {},
            recentErrors: this.errorLog.slice(-10),
            memoryUsage: this.memoryWatchers.get('current')
        };

        // Count error types
        this.errorLog.forEach(error => {
            stats.errorTypes[error.type] = (stats.errorTypes[error.type] || 0) + 1;
        });

        return stats;
    },

    /**
     * Handle ArcGIS specific errors
     * @param {Object} esriError - ArcGIS error object
     */
    handleArcGISError(esriError) {
        const errorInfo = {
            type: 'ArcGIS Error',
            message: esriError.message || 'Unknown ArcGIS error',
            code: esriError.code,
            details: esriError.details,
            timestamp: new Date().toISOString()
        };

        this.logError(errorInfo);

        // Specific handling for common ArcGIS errors
        if (esriError.code === 'network-error') {
            if (typeof window.showNotification === 'function') {
                window.showNotification(
                    'Network connection lost. Retrying...', 
                    'warning', 
                    5000
                );
            }
        } else if (esriError.code === 'authentication-failed') {
            if (typeof window.showNotification === 'function') {
                window.showNotification(
                    'Authentication failed. Please sign in again.', 
                    'error', 
                    8000
                );
            }
        }
    }
};
        
        
        // ==================== AUTH-MANAGER MODULE ====================
        const AuthManager = {
    initialized: false,
    currentUser: null,
    portalUrl: null,
    esriId: null,
    config: null,
    
    /**
     * Initialize the authentication manager
     * @param {Object} esriIdentityManager - ArcGIS Identity Manager
     * @param {Object} config - Application configuration
     * @param {Object} esriClasses - Required ArcGIS classes {OAuthInfo, Portal}
     */
    init(esriIdentityManager, config, esriClasses = {}) {
        this.esriId = esriIdentityManager;
        this.config = config;
        this.portalUrl = config.services.portalUrl;
        this.OAuthInfo = esriClasses.OAuthInfo;
        this.Portal = esriClasses.Portal;
        
        console.log('🔐 Enhanced AuthManager initializing...');
        
        // Check if required classes are available
        if (!this.OAuthInfo || !this.Portal) {
            console.warn('⚠️ Required ArcGIS classes not available, falling back to legacy auth');
            return false;
        }
        
        // Configure OAuth with improved settings
        this.setupOAuth();
        
        // Configure session persistence
        this.setupSessionPersistence();
        
        // Attempt session restoration
        this.restoreSession();
        
        this.initialized = true;
        console.log('✅ Enhanced AuthManager ready');
        return true;
    },

    /**
     * Setup OAuth configuration with improved persistence
     */
    setupOAuth() {
        // Use base portal URL for OAuth (without /home)
        const cleanPortalUrl = this.portalUrl.replace('/home', '');
        
        console.log('🔧 Configuring OAuth with portal:', cleanPortalUrl);
        
        const oauthInfo = new this.OAuthInfo({
            appId: this.config.authentication.appId,
            portalUrl: cleanPortalUrl, // Use clean URL consistently
            popup: false, // Use redirect flow for seamless experience
            flowType: "auto", // Let ArcGIS choose the best flow
            expiration: 20160, // 14 days
            locale: "en",
            preserveUrlHash: true
        });

        this.esriId.registerOAuthInfos([oauthInfo]);
        console.log('✅ OAuth configured with redirect flow');
    },

    /**
     * Setup session persistence with enhanced settings
     */
    setupSessionPersistence() {
        // Enhanced session persistence settings
        this.esriId.useSignInPage = false;
        this.esriId.tokenDuration = 20160; // 14 days in minutes
        
        // Store credentials in localStorage for persistence
        this.esriId.on('credential-create', (evt) => {
            console.log('💾 New credential created, storing for persistence');
            this.storeCredentials();
        });

        // Clear stored credentials on destroy
        this.esriId.on('credentials-destroy', () => {
            console.log('🗑️ Credentials destroyed, clearing storage');
            this.clearStoredCredentials();
        });

        console.log('✅ Session persistence configured');
    },

    /**
     * Attempt to restore existing session
     */
    async restoreSession() {
        console.log('🔄 Attempting session restoration...');

        try {
            // First, try to restore from stored credentials
            await this.restoreStoredCredentials();

            // Then check if we have valid session
            const credential = this.esriId.findCredential(this.portalUrl + "/sharing");
            if (credential) {
                console.log('✅ Found existing credential');
                await this.validateAndUseCredential(credential);
                return;
            }

            // Try checkSignInStatus as fallback
            await this.esriId.checkSignInStatus(this.portalUrl + "/sharing");
            console.log('✅ Active session found via checkSignInStatus');
            await this.handleSuccessfulAuth();

        } catch (error) {
            console.log('ℹ️ No active session found:', error.message);
            this.showSignedOutState();
        }
    },

    /**
     * Store credentials in localStorage for persistence
     */
    storeCredentials() {
        try {
            const credentials = this.esriId.credentials || [];
            const credentialsData = credentials.map(cred => ({
                server: cred.server,
                token: cred.token,
                expires: cred.expires,
                userId: cred.userId,
                ssl: cred.ssl
            }));

            localStorage.setItem('erdb_auth_credentials', JSON.stringify(credentialsData));
            localStorage.setItem('erdb_auth_timestamp', Date.now().toString());
            
            console.log('💾 Credentials stored for persistence');
        } catch (error) {
            console.warn('Failed to store credentials:', error);
        }
    },

    /**
     * Restore credentials from localStorage
     */
    async restoreStoredCredentials() {
        try {
            const storedCredentials = localStorage.getItem('erdb_auth_credentials');
            const timestamp = localStorage.getItem('erdb_auth_timestamp');

            if (!storedCredentials || !timestamp) {
                return false;
            }

            // Check if credentials are not too old (24 hours)
            const age = Date.now() - parseInt(timestamp);
            if (age > 24 * 60 * 60 * 1000) {
                console.log('ℹ️ Stored credentials too old, clearing');
                this.clearStoredCredentials();
                return false;
            }

            const credentialsData = JSON.parse(storedCredentials);
            console.log(`🔄 Restoring ${credentialsData.length} stored credentials`);

            // Restore each credential
            for (const credData of credentialsData) {
                if (credData.expires && Date.now() < credData.expires) {
                    const credential = {
                        server: credData.server,
                        token: credData.token,
                        expires: credData.expires,
                        userId: credData.userId,
                        ssl: credData.ssl
                    };

                    this.esriId.credentials.push(credential);
                    console.log('✅ Restored credential for:', credData.server);
                }
            }

            return true;
        } catch (error) {
            console.warn('Failed to restore credentials:', error);
            return false;
        }
    },

    /**
     * Clear stored credentials
     */
    clearStoredCredentials() {
        localStorage.removeItem('erdb_auth_credentials');
        localStorage.removeItem('erdb_auth_timestamp');
        localStorage.removeItem('erdb_authenticated_user');
        localStorage.removeItem('erdb_auth_timestamp');
    },

    /**
     * Validate and use an existing credential
     */
    async validateAndUseCredential(credential) {
        try {
            // Test the credential by making a simple request
            const testUrl = `${this.portalUrl}/sharing/rest/portals/self?f=json&token=${credential.token}`;
            const response = await fetch(testUrl);
            
            if (response.ok) {
                const data = await response.json();
                if (!data.error) {
                    console.log('✅ Credential validated successfully');
                    await this.handleSuccessfulAuth();
                    return true;
                }
            }
            
            console.log('❌ Credential validation failed, removing');
            this.esriId.destroyCredentials();
            return false;
            
        } catch (error) {
            console.warn('Credential validation error:', error);
            return false;
        }
    },

    /**
     * Handle successful authentication
     */
    async handleSuccessfulAuth() {
        try {
            const portal = new this.Portal({
                url: this.portalUrl
            });

            await portal.load();
            
            const userInfo = {
                username: portal.user.username,
                fullName: portal.user.fullName,
                email: portal.user.email,
                thumbnailUrl: portal.user.thumbnailUrl
            };

            this.currentUser = userInfo;
            
            // Store user info
            localStorage.setItem('erdb_authenticated_user', userInfo.username);
            localStorage.setItem('erdb_auth_timestamp', Date.now().toString());

            // Update UI
            if (typeof window.updateUIAuthenticationState === 'function') {
                window.updateUIAuthenticationState(true, userInfo);
            }

            // Show notification
            if (typeof window.showNotification === 'function') {
                window.showNotification(`Welcome back, ${userInfo.fullName || userInfo.username}!`, 'success', 4000);
            }

            console.log('✅ Authentication successful for:', userInfo.username);
            return userInfo;

        } catch (error) {
            console.error('Error in handleSuccessfulAuth:', error);
            this.showSignedOutState();
            throw error;
        }
    },

    /**
     * Show signed out state
     */
    showSignedOutState() {
        this.currentUser = null;
        
        if (typeof window.updateUIAuthenticationState === 'function') {
            window.updateUIAuthenticationState(false);
        }
    },

    /**
     * Sign in user using direct redirect flow
     */
    async signIn() {
        try {
            console.log('🔐 Initiating sign in with redirect flow...');
            
            if (typeof window.showNotification === 'function') {
                window.showNotification('Redirecting to DENR Portal sign-in...', 'info', 2000);
            }

            // Use getCredential with redirect flow - no popup
            await this.esriId.getCredential(this.portalUrl + "/sharing", {
                oAuthPopupConfirmation: false, // Skip popup confirmation
                error: (error) => {
                    console.error('OAuth error:', error);
                }
            });

            console.log('✅ Sign in successful');
            
            // Store credentials for persistence
            this.storeCredentials();
            
            // Handle successful auth
            await this.handleSuccessfulAuth();

        } catch (error) {
            console.error('Sign in failed:', error);
            
            let errorMessage = 'Sign in failed. Please try again.';
            if (error.name === 'AbortError') {
                errorMessage = 'Sign in timed out. Please try again.';
            } else if (error.message.includes('User cancelled')) {
                errorMessage = 'Sign in cancelled by user.';
            }

            if (typeof window.showNotification === 'function') {
                window.showNotification(errorMessage, 'error', 5000);
            }

            throw error;
        }
    },

    /**
     * Sign out user
     */
    async signOut() {
        try {
            console.log('👋 Signing out user...');
            
            if (typeof window.showNotification === 'function') {
                window.showNotification('Signing out...', 'info', 2000);
            }

            // Destroy credentials
            this.esriId.destroyCredentials();
            
            // Clear stored data
            this.clearStoredCredentials();
            
            // Update state
            this.showSignedOutState();

            console.log('✅ Sign out successful');

        } catch (error) {
            console.error('Sign out error:', error);
        }
    },

    /**
     * Check if user is currently signed in
     */
    isSignedIn() {
        try {
            const credential = this.esriId.findCredential(this.portalUrl + "/sharing");
            return !!credential && !!this.currentUser;
        } catch (error) {
            return false;
        }
    },

    /**
     * Get current user info
     */
    getCurrentUser() {
        return this.currentUser;
    }
};
        
        
        console.log('📦 ERDB modules loaded in bundled mode');
    </script>
    </head>
<body>
<!-- Main title now handled by calcite-navigation-logo -->
<div style="display: none;">
    <h1 id="main-title">DENR Control Map</h1>
</div>


<calcite-shell content-behind>
    <!-- Enhanced Navigation Header -->
    <calcite-navigation slot="header">
        <calcite-navigation-logo
                id="nav-logo"
                slot="logo"
                heading="DENR Infrastructure Map"
                description="Administrative Buildings Control System"
        ></calcite-navigation-logo>
         <calcite-button id="sign-in-button" slot="user" kind="brand" icon-start="sign-in">
            Sign in to DENR Portal
        </calcite-button>
        <calcite-navigation-user hidden id="nav-user" slot="user"> </calcite-navigation-user>
        <div slot="user" style="display: flex; align-items: center; gap: 0.5rem;">
            <calcite-action id="config-button" icon="gear" text="Configuration" onclick="openConfiguration()" style="display: none;">
                <calcite-tooltip slot="tooltip" placement="bottom">Administrative configuration (Admin only)</calcite-tooltip>
            </calcite-action>
            <calcite-action id="filter-button" icon="filter" text="Filter Buildings" onclick="toggleFilterPanel()">
                <calcite-tooltip slot="tooltip" placement="bottom">Open building filters panel</calcite-tooltip>
            </calcite-action>
        </div>
      </calcite-navigation>

    <!-- Collapsible Filter Panel -->
    <calcite-shell-panel slot="panel-start" id="filter-shell-panel" width-scale="m" collapsed>
        <calcite-panel heading="🏢 Building Filters" description="Filter buildings by office level and region">
            <calcite-action slot="header-actions-end" icon="x" text="Close Panel" onclick="toggleFilterPanel()"></calcite-action>
            
            <!-- Filter Content Card -->
            <calcite-card>
                <div slot="title">Filter Criteria</div>
                <div slot="subtitle">Select filters to narrow down building results</div>
                
                <!-- Office Level 2 Filter -->
                <calcite-label style="margin-bottom: 1rem;">
                    Office Level 2
                    <calcite-select id="office-level-2-filter" width="full">
                        <calcite-option value="">All Office Levels</calcite-option>
                    </calcite-select>
                </calcite-label>

                <!-- Region Filter -->
                <calcite-label style="margin-bottom: 1.5rem;">
                    Region
                    <calcite-select id="region-filter" width="full">
                        <calcite-option value="">All Regions</calcite-option>
                    </calcite-select>
                </calcite-label>

                <!-- Action Buttons -->
                <div style="display: flex; gap: 0.5rem;">
                    <calcite-button id="apply-filter" onclick="applyFilters()" width="half" kind="brand">
                        <calcite-icon icon="filter" slot="icon-start"></calcite-icon>
                        Apply Filters
                    </calcite-button>
                    <calcite-button id="clear-filter" onclick="clearFilters()" appearance="outline" width="half">
                        <calcite-icon icon="trash" slot="icon-start"></calcite-icon>
                        Clear
                    </calcite-button>
    </div>

                <!-- Filter Status Notice -->
                <calcite-notice id="filter-status-notice" kind="info" icon="information" style="margin-top: 1rem;">
                    <div slot="message" id="filter-status">No filters active - showing all buildings</div>
                </calcite-notice>
            </calcite-card>
        </calcite-panel>
    </calcite-shell-panel>

    <!-- Hidden Item Panel for Portal Integration -->
    <calcite-panel id="item-panel" hidden>
        <div id="item-gallery"></div>
    </calcite-panel>

    <!-- Main Map Container -->
    <div id="mapContainer"></div>

    <!-- Layer Status Indicator -->
    <calcite-card id="layer-status-card" style="position: absolute; bottom: 24px; left: 24px; z-index: 1000; max-width: 300px; display: none;">
        <div slot="title">Layer Status</div>
        <div slot="subtitle" id="layer-status-subtitle">Loading...</div>
        <div id="layer-status-content">
            <calcite-chip id="feature-count-chip" kind="neutral" scale="s">
                <calcite-icon icon="number" slot="icon"></calcite-icon>
                Features: 0
            </calcite-chip>
            <calcite-chip id="filter-status-chip" kind="neutral" scale="s" style="margin-left: 0.5rem;">
                <calcite-icon icon="filter" slot="icon"></calcite-icon>
                No filters
            </calcite-chip>
        </div>
    </calcite-card>

    <!-- Floating Action Button for Quick Actions -->
    <calcite-fab id="quick-actions-fab" icon="plus" text="Quick Actions" style="position: absolute; bottom: 24px; right: 24px; z-index: 1000;"></calcite-fab>

    <!-- Quick Actions Popover -->
    <calcite-popover id="quick-actions-popover" reference-element="quick-actions-fab" placement="top-end" auto-close>
        <div style="padding: 0.5rem;">
            <calcite-action text="Zoom to All Buildings" icon="magnifying-glass-plus" onclick="zoomToAllBuildings()">
                <calcite-tooltip slot="tooltip" placement="left">Zoom map to show all buildings</calcite-tooltip>
            </calcite-action>
            <calcite-action text="Refresh Data" icon="refresh" onclick="refreshLayerData()">
                <calcite-tooltip slot="tooltip" placement="left">Refresh building data from server</calcite-tooltip>
            </calcite-action>
            <calcite-action text="Export Visible" icon="download" onclick="exportVisibleFeatures()">
                <calcite-tooltip slot="tooltip" placement="left">Export currently visible buildings</calcite-tooltip>
            </calcite-action>
    </div>
    </calcite-popover>

</calcite-shell>
</body>
</html>

