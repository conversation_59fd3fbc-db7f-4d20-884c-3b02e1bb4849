/**
 * Module Loader System
 * Provides dynamic module loading with fallback to inline scripts
 * Supports both development (separate files) and production (bundled) modes
 */

const ModuleLoader = {
    loadedModules: new Set(),
    dependencies: {
        'security': [],
        'app-config': ['security'],
        'ui-manager': ['security', 'app-config'],
        'auth-manager': ['security', 'app-config'],
        'map-manager': ['app-config']
    },

    /**
     * Load a module with its dependencies
     * @param {string} moduleName - Name of the module to load
     * @returns {Promise<void>} Promise that resolves when module is loaded
     */
    async loadModule(moduleName) {
        if (this.loadedModules.has(moduleName)) {
            return Promise.resolve();
        }

        // Load dependencies first
        const deps = this.dependencies[moduleName] || [];
        for (const dep of deps) {
            await this.loadModule(dep);
        }

        // Check if module is already available (bundled mode)
        if (this.isModuleAvailable(moduleName)) {
            this.loadedModules.add(moduleName);
            return Promise.resolve();
        }

        // Try to load from separate file (development mode)
        try {
            await this.loadScript(`js/${moduleName}.js`);
            this.loadedModules.add(moduleName);
            console.log(`Module '${moduleName}' loaded successfully`);
        } catch (error) {
            console.warn(`Failed to load module '${moduleName}':`, error);
            throw new Error(`Module '${moduleName}' not found in bundled or separate file mode`);
        }
    },

    /**
     * Check if module is already available
     * @param {string} moduleName - Name of the module
     * @returns {boolean} True if module is available
     */
    isModuleAvailable(moduleName) {
        const moduleMap = {
            'security': () => typeof SecurityUtils !== 'undefined',
            'app-config': () => typeof AppConfig !== 'undefined',
            'ui-manager': () => typeof UIManager !== 'undefined',
            'auth-manager': () => typeof AuthManager !== 'undefined',
            'map-manager': () => typeof MapManager !== 'undefined'
        };

        return moduleMap[moduleName] ? moduleMap[moduleName]() : false;
    },

    /**
     * Load a script file
     * @param {string} src - Script source URL
     * @returns {Promise<void>} Promise that resolves when script is loaded
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            // Check if script is already loaded
            const existing = document.querySelector(`script[src="${src}"]`);
            if (existing) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            
            script.onload = () => resolve();
            script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
            
            document.head.appendChild(script);
        });
    },

    /**
     * Initialize application with required modules
     * @param {Array<string>} modules - List of required modules
     * @param {Function} callback - Callback to execute after loading
     * @returns {Promise<void>} Promise that resolves when initialization is complete
     */
    async initializeApp(modules, callback) {
        try {
            console.log('Loading application modules:', modules);
            
            // Load all required modules
            for (const module of modules) {
                await this.loadModule(module);
            }
            
            console.log('All modules loaded successfully');
            
            // Execute callback if provided
            if (typeof callback === 'function') {
                callback();
            }
            
        } catch (error) {
            console.error('Module loading failed:', error);
            
            // Show user-friendly error if notification system is available
            if (typeof window.showNotification === 'function') {
                window.showNotification(
                    'Application loading failed. Please refresh the page.', 
                    'error', 
                    10000
                );
            } else {
                alert('Application loading failed. Please refresh the page.');
            }
            
            throw error;
        }
    }
};

// Make available globally
if (typeof window !== 'undefined') {
    window.ModuleLoader = ModuleLoader;
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModuleLoader;
}
