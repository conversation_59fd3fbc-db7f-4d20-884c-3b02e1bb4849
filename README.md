# ERDB Control Map - Simple Configuration System

## 🚀 Quick Start

**Super Simple:** Just open `index.html` in any web browser. That's it!

- **No installation required**
- **No server setup needed**  
- **No dependencies**
- **Works offline**

## ⚙️ Configuration

### Dedicated Configuration Interface

Click the **⚙️ gear icon** in the top-right corner to open the dedicated configuration page (`config.html`) where you can:

- **Application Settings**: Change title and description
- **Map Settings**: Set center coordinates, zoom level, and basemap
- **Service URLs**: Configure portal and feature layer URLs
- **Authentication**: Set OAuth application ID

Changes are **automatically saved** to your browser and **persist** between sessions.

### Quick Configuration via URL Parameters

Test different settings instantly by adding parameters to the URL:

```
# Change basemap to satellite
index.html?basemap=satellite

# Set different center and zoom
index.html?center=[120,15]&zoom=8

# Use different portal
index.html?portalUrl=https://your-portal.com

# Multiple parameters
index.html?basemap=streets&zoom=10&debug=true
```

### Easy Access

- **⚙️ Gear Icon** - Opens dedicated configuration page
- **Back to Map** - Return to main application from config page

## 📖 Configuration Options

### Map Settings
- **Center**: Map center coordinates as `[longitude, latitude]`
- **Zoom**: Initial zoom level (1-20)
- **Basemap**: Choose from:
  - `topo` - Topographic (default)
  - `streets` - Street map
  - `satellite` - Satellite imagery
  - `hybrid` - Satellite with labels
  - `terrain` - Terrain map
  - `osm` - OpenStreetMap
  - `dark-gray` - Dark theme
  - `gray` - Light gray theme

### Service URLs
- **Portal URL**: ArcGIS Portal or ArcGIS Online URL
- **Feature Layer URL**: URL to your feature service

### Authentication
- **App ID**: OAuth application ID for ArcGIS authentication

## 🔧 Advanced Usage

### Reset to Defaults
- Use the "Reset to Defaults" button in the configuration panel
- Or clear browser storage: `localStorage.removeItem('erdb-config')`

### Debug Mode
Add `?debug=true` to the URL to see detailed console logging:
```
index.html?debug=true
```

### Export/Import Configuration
Open browser console and use:
```javascript
// Export current configuration
console.log(JSON.stringify(AppConfig.load(), null, 2));

// Import configuration
AppConfig.save(yourConfigObject);
```

## 🌐 Deployment

### Local Use
Just open `index.html` in any modern web browser.

### Web Server
Upload `index.html` to any web server. No special configuration needed.

### Customization
1. Open the configuration panel (⚙️ icon)
2. Modify settings as needed
3. Save changes
4. Share the HTML file with your custom settings embedded

## 🛠️ Technical Details

### Data Storage
- Configuration stored in browser's localStorage
- No external files or databases required
- Settings persist across browser sessions
- Each browser/device maintains its own settings

### Browser Compatibility
Works in all modern browsers that support:
- ES6 JavaScript
- localStorage API
- ArcGIS API for JavaScript 4.29

### No CORS Issues
- All configuration embedded in HTML/JavaScript
- No external file loading required
- Works from file:// protocol
- No server dependencies

## 🎯 Use Cases

### Personal Use
- Open and configure for your area of interest
- Settings automatically saved in your browser

### Organization Deployment
- Configure once with your organization's settings
- Distribute the configured HTML file
- Each user gets the same default configuration
- Users can still customize locally if needed

### Multiple Configurations
- Create different HTML files for different projects
- Use URL parameters for quick testing
- Share specific configurations via URL

## 📞 Support

### Common Tasks

**Change the map center:**
1. Click ⚙️ gear icon
2. Enter new longitude/latitude
3. Click "Save & Apply"

**Switch to satellite view:**
- Add `?basemap=satellite` to URL, or
- Use configuration panel to change default

**Reset everything:**
- Use "Reset to Defaults" button in config panel

### Troubleshooting

**Map not loading:**
- Check browser console for errors (F12)
- Verify service URLs in configuration
- Ensure internet connection for ArcGIS services

**Configuration not saving:**
- Check if browser allows localStorage
- Try incognito/private mode to test
- Clear browser cache and try again

**Authentication issues:**
- Verify App ID in configuration
- Check if portal URL is correct
- Ensure HTTPS for production authentication

## 📄 License

This project is licensed under the terms specified by the DENR-ERDB organization.

---

**🎉 That's it! Simple, no-fuss mapping with dynamic configuration.**