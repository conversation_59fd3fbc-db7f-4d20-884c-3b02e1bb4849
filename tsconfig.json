{"compilerOptions": {"target": "ES2017", "lib": ["ES2017", "DOM", "DOM.Iterable"], "allowJs": true, "checkJs": false, "declaration": false, "declarationMap": false, "sourceMap": false, "outDir": "./dist", "removeComments": false, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitReturns": false, "noImplicitThis": false, "alwaysStrict": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "noImplicitUseStrict": false, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["./js/*"], "@types/*": ["./types/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true}, "include": ["js/**/*", "types/**/*", "*.js", "*.html"], "exclude": ["node_modules", "dist"], "typeAcquisition": {"enable": true, "include": ["@types/arcgis-js-api"]}}