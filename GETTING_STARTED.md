# 🚀 Getting Started - ERDB Control Map

## Super Simple Setup (30 seconds)

1. **Open** `index.html` in any web browser
2. **Done!** 🎉

No installation. No setup. No complications.

## 🎯 First Steps

### 1. Customize Your Map
Click the **⚙️ gear icon** (top-right corner) to open the configuration page where you can configure:

- **Map Center**: Set to your area of interest
- **Basemap**: Choose satellite, streets, or topographic
- **Service URLs**: Point to your ArcGIS services
- **App Title**: Personalize the application name

### 2. Quick Testing
Try these URLs to see instant changes:

```
# Satellite view with higher zoom
index.html?basemap=satellite&zoom=10

# Different location (Manila)
index.html?center=[121.0244,14.6349]&zoom=12

# Streets view
index.html?basemap=streets
```

### 3. Save Your Settings
All changes in the configuration interface are **automatically saved** to your browser and will persist when you reopen the application.

## 🔧 Common Customizations

### Change Map Location
1. Click ⚙️ → Map Settings
2. Enter your coordinates:
   - **Longitude**: East/West position (e.g., 121.0244 for Manila)
   - **Latitude**: North/South position (e.g., 14.6349 for Manila)
3. Click "Save & Apply"

### Switch Basemap
1. Click ⚙️ → Map Settings
2. Choose from dropdown:
   - **Topographic**: Detailed terrain (default)
   - **Satellite**: Aerial imagery
   - **Streets**: Road network
   - **Hybrid**: Satellite + labels

### Connect to Your Portal
1. Click ⚙️ → Service URLs
2. Enter your ArcGIS Portal URL
3. Enter your Feature Layer URL
4. Click ⚙️ → Authentication
5. Enter your OAuth App ID

## 🎮 Tips & Tricks

### Keyboard Shortcuts
- **Ctrl+,** = Open settings
- **Escape** = Close settings

### URL Magic
Add these to your URL for instant changes:
- `?basemap=satellite` - Satellite view
- `?zoom=15` - Zoom level
- `?center=[lng,lat]` - Map center
- `?debug=true` - Show debug info

### Multiple Configurations
- **Bookmark** different URL combinations
- **Copy** the HTML file for different projects
- **Share** URLs with specific settings

### Reset Everything
If something goes wrong:
1. Click ⚙️ → "Reset to Defaults"
2. Or open browser console (F12) and type: `localStorage.clear()`

## 🌐 Sharing & Deployment

### Share with Others
1. Configure your settings
2. Copy the `index.html` file
3. Share it - recipients get your configuration as defaults
4. They can still customize locally

### Web Deployment
- Upload `index.html` to any web server
- No database or backend required
- Works immediately

### Organization Use
- Set up with organizational settings
- Distribute to team members
- Everyone gets consistent defaults
- Individual customization still possible

## ❓ Need Help?

### Quick Fixes
- **Map not loading?** Check internet connection
- **Settings not saving?** Check browser localStorage is enabled  
- **Wrong location?** Use ⚙️ to set correct coordinates
- **Authentication failing?** Verify Portal URL and App ID

### Debug Mode
Add `?debug=true` to your URL to see detailed console information.

### Start Fresh
Click "Reset to Defaults" in settings to return to original configuration.

---

**🗺️ Happy Mapping!**

*Everything you need is in one simple HTML file.*