#!/usr/bin/env node

/**
 * Build Script for ERDB Control Map
 * Combines modular JavaScript files into single HTML files for deployment
 * Maintains compatibility with both development (modular) and production (bundled) modes
 */

const fs = require('fs');
const path = require('path');

class ERDBBuilder {
    constructor() {
        this.jsModules = {
            'security': 'js/security.js',
            'app-config': 'js/app-config.js',
            'module-loader': 'js/module-loader.js',
            'error-manager': 'js/error-manager.js',
            'auth-manager': 'js/auth-manager.js'
        };
        
        this.htmlFiles = {
            'index.html': 'dist/index.html',
            'config.html': 'dist/config.html'
        };
    }

    /**
     * Main build process
     */
    async build() {
        console.log('🚀 Starting ERDB Control Map build process...\n');
        
        try {
            // Create dist directory
            this.ensureDistDirectory();
            
            // Load all JavaScript modules
            const modules = await this.loadModules();
            
            // Process each HTML file
            for (const [sourceFile, targetFile] of Object.entries(this.htmlFiles)) {
                await this.processHtmlFile(sourceFile, targetFile, modules);
            }
            
            console.log('\n✅ Build completed successfully!');
            console.log('📁 Output files:');
            Object.values(this.htmlFiles).forEach(file => {
                console.log(`   - ${file}`);
            });
            
        } catch (error) {
            console.error('\n❌ Build failed:', error.message);
            process.exit(1);
        }
    }

    /**
     * Ensure dist directory exists
     */
    ensureDistDirectory() {
        const distDir = path.dirname(Object.values(this.htmlFiles)[0]);
        if (!fs.existsSync(distDir)) {
            fs.mkdirSync(distDir, { recursive: true });
            console.log(`📁 Created directory: ${distDir}`);
        }
    }

    /**
     * Load all JavaScript modules
     * @returns {Object} Map of module names to content
     */
    async loadModules() {
        console.log('📦 Loading JavaScript modules...');
        const modules = {};
        
        for (const [name, filePath] of Object.entries(this.jsModules)) {
            if (fs.existsSync(filePath)) {
                modules[name] = fs.readFileSync(filePath, 'utf8');
                console.log(`   ✓ Loaded: ${name} (${filePath})`);
            } else {
                console.warn(`   ⚠️  Missing: ${name} (${filePath})`);
            }
        }
        
        return modules;
    }

    /**
     * Process HTML file and inject modules
     * @param {string} sourceFile - Source HTML file path
     * @param {string} targetFile - Target HTML file path
     * @param {Object} modules - Loaded JavaScript modules
     */
    async processHtmlFile(sourceFile, targetFile, modules) {
        console.log(`\n🔄 Processing: ${sourceFile} → ${targetFile}`);
        
        if (!fs.existsSync(sourceFile)) {
            throw new Error(`Source file not found: ${sourceFile}`);
        }
        
        let htmlContent = fs.readFileSync(sourceFile, 'utf8');
        
        // Remove existing module script tags if any
        htmlContent = this.removeModuleScripts(htmlContent);
        
        // Inject bundled modules before the main script
        htmlContent = this.injectBundledModules(htmlContent, modules);
        
        // Update script references to use bundled modules
        htmlContent = this.updateScriptReferences(htmlContent);
        
        // Write processed file
        fs.writeFileSync(targetFile, htmlContent, 'utf8');
        
        console.log(`   ✓ Generated: ${targetFile}`);
        console.log(`   📊 Size: ${this.formatFileSize(fs.statSync(targetFile).size)}`);
    }

    /**
     * Remove existing module script tags
     * @param {string} content - HTML content
     * @returns {string} Content with module scripts removed
     */
    removeModuleScripts(content) {
        // Remove script tags that load modular files
        return content.replace(/<script[^>]*src=["']js\/[^"']*["'][^>]*><\/script>/g, '');
    }

    /**
     * Inject bundled modules into HTML
     * @param {string} content - HTML content
     * @param {Object} modules - JavaScript modules
     * @returns {string} Content with injected modules
     */
    injectBundledModules(content, modules) {
        const bundledScript = this.createBundledScript(modules);
        
        // Find the closing </head> tag and insert before it
        const headCloseIndex = content.lastIndexOf('</head>');
        if (headCloseIndex === -1) {
            throw new Error('No </head> tag found in HTML');
        }
        
        return content.slice(0, headCloseIndex) + 
               bundledScript + '\n    ' +
               content.slice(headCloseIndex);
    }

    /**
     * Create bundled script tag with all modules
     * @param {Object} modules - JavaScript modules
     * @returns {string} Script tag with bundled content
     */
    createBundledScript(modules) {
        const moduleOrder = ['security', 'app-config', 'module-loader'];
        let bundledContent = '';
        
        // Add header comment
        bundledContent += `
    <script>
        /* 
         * ERDB Control Map - Bundled Modules
         * Generated on: ${new Date().toISOString()}
         * Modules included: ${Object.keys(modules).join(', ')}
         */
        
        // Module bundling flag
        window.ERDB_BUNDLED_MODE = true;
        
        `;
        
        // Add modules in dependency order
        for (const moduleName of ['security', 'app-config', 'module-loader', 'error-manager', 'auth-manager']) {
            if (modules[moduleName]) {
                bundledContent += `
        // ==================== ${moduleName.toUpperCase()} MODULE ====================
        ${this.cleanModuleCode(modules[moduleName])}
        
        `;
            }
        }
        
        bundledContent += `
        console.log('📦 ERDB modules loaded in bundled mode');
    </script>`;
        
        return bundledContent;
    }

    /**
     * Clean module code for bundling
     * @param {string} code - Module code
     * @returns {string} Cleaned code
     */
    cleanModuleCode(code) {
        // Remove module export statements and comments at the beginning
        return code
            .replace(/^\/\*\*[\s\S]*?\*\/\s*/m, '') // Remove JSDoc header
            .replace(/\/\/ Export for module use[\s\S]*$/m, '') // Remove export section
            .trim();
    }

    /**
     * Update script references to use bundled modules
     * @param {string} content - HTML content
     * @returns {string} Updated content
     */
    updateScriptReferences(content) {
        // Add bundled mode checks to main scripts
        const bundleChecks = `
        // Check if modules are bundled, otherwise load them dynamically
        if (!window.ERDB_BUNDLED_MODE && typeof ModuleLoader !== 'undefined') {
            ModuleLoader.initializeApp(['security', 'app-config'], initializeApplication);
        } else {
            // Bundled mode - proceed directly
            initializeApplication();
        }`;
        
        // Replace direct initialization calls with bundle-aware initialization
        content = content.replace(
            /initializeApplication\(\);?\s*$/gm, 
            bundleChecks
        );
        
        return content;
    }

    /**
     * Format file size for display
     * @param {number} bytes - File size in bytes
     * @returns {string} Formatted size
     */
    formatFileSize(bytes) {
        const kb = bytes / 1024;
        const mb = kb / 1024;
        
        if (mb >= 1) {
            return `${mb.toFixed(2)} MB`;
        } else if (kb >= 1) {
            return `${kb.toFixed(2)} KB`;
        } else {
            return `${bytes} bytes`;
        }
    }
}

// CLI interface
if (require.main === module) {
    const builder = new ERDBBuilder();
    builder.build().catch(error => {
        console.error('Build failed:', error);
        process.exit(1);
    });
}

module.exports = ERDBBuilder;
