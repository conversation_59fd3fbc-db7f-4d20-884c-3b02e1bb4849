<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
    <title id="page-title">DENR Control Map</title>
    <!-- Prevent favicon 500 errors by providing a data URL favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
    <style>
        html, body, #viewDiv {
            padding: 0;
            margin: 0;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }
        
        /* Responsive layout system */
        #mapContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: var(--map-height, 70%);
            transition: height 0.3s ease;
        }
        
        
        /* Full-screen map layout */
        #mapContainer {
            height: 100% !important;
        }
        
        /* Enhanced Mobile responsiveness with Calcite */
        @media (max-width: 768px) {
            /* Touch-friendly interface */
            .esri-ui-top-left .esri-component,
            .esri-ui-top-right .esri-component {
                margin: 8px;
            }
            
            /* Larger touch targets */
            .esri-widget button,
            .esri-widget .esri-widget__button {
                min-height: 44px;
                min-width: 44px;
            }

            /* Mobile filter panel adjustments */
            calcite-shell-panel[slot="panel-start"] {
                --calcite-shell-panel-width: 90vw;
                --calcite-shell-panel-max-width: 350px;
            }
            
            /* Mobile navigation improvements */
            calcite-navigation {
                --calcite-navigation-height: 60px;
            }
            
            /* Hide title on very small screens */
            #main-title {
                display: none;
            }
        }

        @media (max-width: 480px) {
            /* Very small screens - compact filter controls */
            calcite-card {
                --calcite-card-spacing: 0.75rem;
            }
            
            calcite-label {
                margin-bottom: 0.75rem !important;
            }
        }
        
        /* Calcite loading states */
        .calcite-loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(3px);
        }
        
        .calcite-loading-content {
        text-align: center;
            padding: 2rem;
            background: var(--calcite-color-foreground-1);
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            max-width: 300px;
        }
        
        /* Calcite notification container */
        .calcite-notification-container {
            position: fixed;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            max-width: 90%;
            width: auto;
            min-width: 300px;
        }

        /* Enhanced Calcite integration */
        calcite-notice {
            margin-bottom: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        /* DENR Theme Integration */
        :root {
            --calcite-color-brand: #2E7D32;
            --calcite-color-brand-hover: #1B5E20;
            --calcite-color-brand-press: #388E3C;
        }

        /* Enhanced navigation styling */
        calcite-navigation {
            --calcite-navigation-user-display: flex;
            --calcite-navigation-user-alignment: center;
        }

        /* Ensure navigation actions are properly spaced */
        calcite-navigation [slot="user"] {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Configuration button styling when visible */
        #config-button[style*="inline-flex"] {
            display: inline-flex !important;
        }

        /* Enhanced sign-in button styling */
        #sign-in-button {
            transition: all 0.3s ease;
        }

        #sign-in-button[loading] {
            --calcite-color-brand: #66BB6A;
            cursor: wait;
        }

        #sign-in-button[disabled] {
            opacity: 0.7;
            pointer-events: none;
        }

        /* Custom login notification styling */
        .login-notification {
            --calcite-notice-spacing: 1rem;
        }
        .container {
            height: 100%;
            width: 100%;
      }

    
    </style>
    <link rel="stylesheet" href="https://js.arcgis.com/4.33/esri/themes/light/main.css" />
    <link rel="stylesheet" type="text/css" href="https://js.arcgis.com/calcite-components/2.13.2/calcite.css" />
    <script src="https://js.arcgis.com/4.33/"></script>
    <script type="module" src="https://js.arcgis.com/calcite-components/2.13.2/calcite.esm.js"></script>
    <!-- Module loading scripts -->
    <script src="js/module-loader.js"></script>
    <script src="js/security.js"></script>
    <script src="js/app-config.js"></script>
    <script src="js/error-manager.js"></script>
    <script src="js/auth-manager.js"></script>
    
    <script>

        // AppConfig is now loaded from external module
        // Note: AppConfig is now available from js/app-config.js

        // Configuration loader utility with enhanced validation
        async function loadConfiguration() {
            let config = AppConfig.load();
            config = AppConfig.applyUrlOverrides(config);
            
            // Decrypt password if encrypted
            if (config.authentication && config.authentication.encrypted && config.authentication.password) {
                try {
                    const deviceId = await AppConfig.getDeviceId();
                    const decryptedPassword = await SecurityUtils.decryptData(config.authentication.password, deviceId);
                    config.authentication.password = decryptedPassword;
                    config.authentication.encrypted = false; // Mark as decrypted for current session
                } catch (error) {
                    console.warn('Could not decrypt stored password:', error);
                    config.authentication.password = '';
                    showNotification('Stored credentials could not be decrypted. Please re-enter your password.', 'warning');
                }
            }
            
            // Log configuration source
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('debug') === 'true') {
                // Don't log sensitive information
                const safeConfig = { ...config };
                if (safeConfig.authentication && safeConfig.authentication.password) {
                    safeConfig.authentication.password = '[ENCRYPTED]';
                }
                console.log('Final configuration:', safeConfig);
            }
            
            return config;
        }

        // Auto-diagnostic function to detect and fix common issues
        function runStartupDiagnostics(config) {
            console.log('Running startup diagnostics...');
            
            let issuesFound = [];
            let autoFixes = [];
            let configUpdated = false;
            
            // Check for MapServer URL that should be FeatureServer
            if (config.services.featureLayerUrl && config.services.featureLayerUrl.includes('MapServer')) {
                issuesFound.push('MapServer URL detected (should be FeatureServer for editing)');
                const correctedUrl = config.services.featureLayerUrl.replace('MapServer', 'FeatureServer');
                config.services.featureLayerUrl = correctedUrl;
                autoFixes.push(`Auto-corrected: MapServer → FeatureServer (${correctedUrl})`);
                configUpdated = true;
            }
            
            // Check for old portal URLs (only the old erdb subdomain)
            if (config.services.portalUrl && config.services.portalUrl.includes('controlmap-erdb.denr.gov.ph')) {
                issuesFound.push('Old portal URL detected (controlmap-erdb)');
                config.services.portalUrl = AppConfig.default.services.portalUrl;
                autoFixes.push('Auto-corrected: Updated from controlmap-erdb to controlmap');
                configUpdated = true;
            }
            
            // Save configuration if any fixes were applied
            if (configUpdated) {
                AppConfig.save(config);
            }
            
            // Report diagnostics
            if (issuesFound.length > 0) {
                console.warn('Startup issues detected:', issuesFound);
                console.log('Auto-fixes applied:', autoFixes);
                
                // Show notification about auto-fixes
                setTimeout(() => {
                    showNotification(
                        `🔧 Auto-fixes applied: ${autoFixes.join(', ')}`,
                        'success',
                        6000
                    );
                }, 2000);
            } else {
                console.log('✅ No configuration issues detected');
            }
            
            return config;
        }

        // Wait for DOM to be fully loaded before initializing
        function initializeApplication() {
        loadConfiguration().then((config) => {
                try {
                    // Run startup diagnostics and auto-fixes
                    config = runStartupDiagnostics(config);
                    // Update HTML elements with configuration values (with null checks)
                    const pageTitle = document.getElementById('page-title');
                    if (pageTitle) pageTitle.textContent = config.app.title;
                    
                    const mainTitle = document.getElementById('main-title');
                    if (mainTitle) mainTitle.textContent = config.app.title;
                    
                    const navLogo = document.getElementById('nav-logo');
                    if (navLogo) {
                        navLogo.setAttribute('heading', config.app.title);
                        navLogo.setAttribute('description', config.app.description);
                    }
                    
                    // Full-screen map layout - no additional CSS properties needed
                    
            // Initialize enhanced authentication BEFORE ArcGIS components, so secured layers can load
            if (typeof AuthManager !== 'undefined') {
                console.log('🔐 Initializing enhanced authentication...');
                AuthManager.init(esriId, config, { OAuthInfo, Portal });
            }

            // Initialize ArcGIS components after auth init
            initializeArcGISComponents(config);
                    
                } catch (error) {
                    console.error('Error during application initialization:', error);
                    showNotification('Application initialization failed. Please refresh the page.', 'error');
                }
            }).catch((error) => {
                console.error('Error loading configuration:', error);
                showNotification('Configuration loading failed. Using default settings.', 'warning');
                // Fallback to default configuration
                initializeArcGISComponents(AppConfig.default);
            });
        }

        // Initialize ArcGIS components
        function initializeArcGISComponents(config) {
            require([
                "esri/config",
                "esri/Map",
                "esri/views/MapView",
                "esri/core/reactiveUtils",
                "esri/request",
                "esri/layers/FeatureLayer",
                "esri/Graphic",
                "esri/widgets/Editor",
                "esri/portal/Portal",
                "esri/identity/OAuthInfo",
                "esri/identity/IdentityManager",
                "esri/portal/PortalQueryParams",
                "esri/core/Error"
            ], (esriConfig, Map, MapView, reactiveUtils, request, FeatureLayer, Graphic, Editor, Portal, OAuthInfo, esriId, PortalQueryParams, EsriError) => {

                let selectionIdCount = 0; // The filtered selection id count
                let candidate; // The graphic accessed via the view.click event
                let featureLayerView;

                // Use configuration values
                const portalUrl = config.services.portalUrl;
                const featureLayerUrl = config.services.featureLayerUrl;

                console.log('Loading feature layer from URL:', featureLayerUrl);

                esriConfig.portalUrl = portalUrl;
        
            // Upload functionality removed

            const map = new Map({
                basemap: config.map.basemap
            });
/*
            const view = new MapView({
                center: [122, 12],
                zoom: 6,
                map: map,
                container: "mapContainer",
                popup: {
                    defaultPopupTemplateEnabled: true
                }
            });
*/
             const view = new MapView({
                center: config.map.center,
                zoom: config.map.zoom,
                map: map,
                container: "mapContainer"
            });

            const popupTemplate = {
                title: config.popupTemplate.title,
                content: [{
                    type: "fields",
                    fieldInfos: config.popupTemplate.fieldInfos
                }]
            };
             
        
            // File upload functionality removed

            // Enhanced error handling with user-friendly notifications
            function errorHandler(error) {
                console.error('Application error:', error);
                
                // Show notification
                showNotification(error.message, 'error');
            }
            
            // Modern Calcite notification system
            function showNotification(message, type = 'info', duration = 5000) {
                // Create notification container if it doesn't exist
                let container = document.querySelector('.calcite-notification-container');
                if (!container) {
                    container = document.createElement('div');
                    container.className = 'calcite-notification-container';
                    document.body.appendChild(container);
                }

                // Map types to Calcite notice kinds
                const kindMap = {
                    'info': 'brand',
                    'success': 'success', 
                    'warning': 'warning',
                    'error': 'danger'
                };

                // Map types to icons
                const iconMap = {
                    'info': 'information',
                    'success': 'check-circle',
                    'warning': 'exclamation-mark-triangle',
                    'error': 'exclamation-mark-triangle-f'
                };

                // Create Calcite notice
                const notice = document.createElement('calcite-notice');
                notice.setAttribute('open', '');
                notice.setAttribute('kind', kindMap[type] || 'brand');
                notice.setAttribute('icon', iconMap[type] || 'information');
                notice.setAttribute('auto-close', '');
                notice.setAttribute('auto-close-duration', duration.toString());

                // Create notice message
                const noticeMessage = document.createElement('div');
                noticeMessage.setAttribute('slot', 'message');
                noticeMessage.textContent = SecurityUtils.validateAndSanitize.text(message);
                notice.appendChild(noticeMessage);

                container.appendChild(notice);

                // Auto-remove from DOM after animation
                setTimeout(() => {
                    if (notice.parentNode) {
                        notice.remove();
                    }
                }, duration + 500);
                
                return notice;
            }
            
            // Modern Calcite loading overlay
            function showLoadingOverlay(container = document.body, message = 'Loading...') {
                const overlay = document.createElement('div');
                overlay.className = 'calcite-loading-overlay';
                
                const content = document.createElement('div');
                content.className = 'calcite-loading-content';
                
                const loader = document.createElement('calcite-loader');
                loader.setAttribute('active', '');
                loader.setAttribute('label', SecurityUtils.validateAndSanitize.text(message));
                loader.setAttribute('text', SecurityUtils.validateAndSanitize.text(message));
                
                content.appendChild(loader);
                overlay.appendChild(content);
                container.appendChild(overlay);
                
                return overlay;
            }
            
            function hideLoadingOverlay(overlay) {
                if (overlay && overlay.parentNode) {
                    overlay.remove();
                }
            }

            // Shapefile upload functionality removed

            // Modern Basemap Toggle using web component
            const basemapToggle = document.createElement("arcgis-basemap-toggle");
            basemapToggle.view = view;
            basemapToggle.nextBasemap = config.map.alternateBasemap;

            view.ui.add(basemapToggle, "bottom-right");

            // Enhanced Feature Layer with conditional clustering
            var featureLayer = new FeatureLayer({
                url: featureLayerUrl,
                outFields: ["*"], // Include all fields in feature attributes
                popupTemplate: popupTemplate,
                // Performance optimizations
                maxRecordCountFactor: 2,
                minScale: 0,
                maxScale: 0
                // Note: Feature reduction (clustering) will be added conditionally based on feature count
            });

            // Add error handling for feature layer with improved notifications
            featureLayer.when(() => {
                console.log("Feature layer loaded successfully:", featureLayer.title || featureLayer.url);
                
                // Log layer capabilities for debugging
                if (config.debug || new URLSearchParams(window.location.search).get('debug') === 'true') {
                    console.log("Layer capabilities:", featureLayer.capabilities);
                    console.log("Layer fields:", featureLayer.fields);
                    console.log("Layer geometry type:", featureLayer.geometryType);
                }
                
                // Show success notification
                showNotification("Feature layer loaded successfully", "success", 3000);
                
                // Configure performance-based rendering
                if (featureLayer.capabilities && featureLayer.capabilities.query) {
                    featureLayer.queryFeatureCount().then(count => {
                        console.log(`Feature layer contains ${count} features`);
                        
                        // Update layer status indicator
                        window.updateLayerStatus(count, featureLayer.title || 'DENR Administrative Buildings');
                        
                        // Only enable clustering for very large datasets (>1000 features)
                        // With 83 features, we'll show actual geometries
                        if (count > 1000) {
                            console.log("Large dataset detected - enabling clustering for performance");
                            featureLayer.featureReduction = {
                                type: "cluster",
                                clusterRadius: count > 5000 ? "150px" : "100px",
                                clusterMinSize: "24px",
                                clusterMaxSize: "60px",
                                labelingInfo: [{
                                    deconflictionStrategy: "none",
                                    labelExpressionInfo: {
                                        expression: "Text($feature.cluster_count, '#,###')"
                                    },
                                    symbol: {
                                        type: "text",
                                        color: "#004c73",
                                        font: {
                                            weight: "bold",
                                            family: "Noto Sans",
                                            size: "12px"
                                        }
                                    },
                                    labelPlacement: "center-center"
                                }]
                            };
                            
                            // Add clustering toggle button
                            addClusteringToggle(featureLayer, count);
                        } else {
                            console.log("Small dataset - showing actual feature geometries");
                            showNotification(`Displaying ${count} features with actual geometries 🗺️`, 'success', 4000);
                        }
                    }).catch(e => console.warn("Could not query feature count:", e));
                }
                
            }).catch((error) => {
                console.error("Error loading feature layer:", error);
                
                // Check if this is a MapServer URL issue
                let errorMessage = error.message || 'Unknown error';
                let fixSuggestion = '';
                
                if (featureLayerUrl.includes('MapServer')) {
                    fixSuggestion = ' This appears to be a MapServer URL - try using FeatureServer instead for editing capabilities.';
                    
                    // Auto-attempt to fix MapServer to FeatureServer
                    const correctedUrl = featureLayerUrl.replace('MapServer', 'FeatureServer');
                    console.warn('Attempting to auto-correct MapServer URL to FeatureServer:', correctedUrl);
                    
                    // Update configuration with corrected URL
                    const currentConfig = AppConfig.load();
                    currentConfig.services.featureLayerUrl = correctedUrl;
                    AppConfig.save(currentConfig);
                    
                    showNotification(
                        `Auto-correcting URL: MapServer → FeatureServer. Please refresh the page to try the corrected URL.`,
                        'warning',
                        8000
                    );
                } else if (errorMessage.includes('not found') || errorMessage.includes('404')) {
                    fixSuggestion = ' The service may be unavailable or the URL may be incorrect. Please check the configuration.';
                } else if (errorMessage.includes('CORS') || errorMessage.includes('Access-Control')) {
                    fixSuggestion = ' This appears to be a CORS issue. Try clearing the cache or updating the portal URL.';
                }
                
                // Show user-friendly error notification
                showNotification(
                    `Layer Loading Error: Unable to load feature layer from ${featureLayerUrl}. ${errorMessage}${fixSuggestion}`,
                    'error',
                    15000
                );
                
                // If this is a MapServer issue and auto-correction was applied, show refresh button
                if (featureLayerUrl.includes('MapServer')) {
                setTimeout(() => {
                        if (confirm('The URL has been auto-corrected from MapServer to FeatureServer. Would you like to refresh the page now to apply the fix?')) {
                            window.location.reload();
                    }
                    }, 2000);
                }
            });

            map.add(featureLayer);

            // Enhanced Editor widget
            var editor = new Editor({
                view: view,
                allowedWorkflows: ["create", "update", "delete"], // Specify allowed operations
                visibleElements: {
                    createWorkflow: {
                        creationMode: "single"
                    }
                }
            });

            view.ui.add(editor, "top-right");

            // Search widget removed for simplified interface

            // LayerList and Legend widgets removed for simplified interface


            // Feature table removed for simplified map-only interface

        
            // Enhanced OAuth 2.0 authentication using AuthManager
            console.log('🔐 Initializing enhanced authentication...');

            // UI elements
            const signInButton = document.getElementById("sign-in-button");
            const navLogo = document.getElementById("nav-logo");
            const navigationUser = document.getElementById("nav-user");
            
            // Initialize AuthManager with esriId, config, and required classes
            if (typeof AuthManager !== 'undefined') {
                const authInitialized = AuthManager.init(esriId, config, {
                    OAuthInfo: OAuthInfo,
                    Portal: Portal
                });
                
                if (authInitialized) {
                    // Setup sign in/out handlers
                    if (signInButton) {
                        signInButton.addEventListener("click", async () => {
                            if (AuthManager.isSignedIn()) {
                                await AuthManager.signOut();
                            } else {
                                await AuthManager.signIn();
                            }
                        });
                    }
                    
                    if (navigationUser) {
                        navigationUser.addEventListener("click", async () => {
                            if (AuthManager.isSignedIn()) {
                                await AuthManager.signOut();
                            } else {
                                await AuthManager.signIn();
                            }
                        });
                    }
                    
                    console.log('✅ Enhanced authentication initialized');
                } else {
                    console.warn('AuthManager initialization failed, falling back to legacy auth');
                    legacyAuthSetup();
                }
            } else {
                console.warn('AuthManager not available, falling back to legacy auth');
                // Fallback to original authentication logic
                legacyAuthSetup();
            }

            function legacyAuthSetup() {
                // Original authentication code as fallback
                const loginPortalUrl = portalUrl.endsWith('/home') ? portalUrl : portalUrl + '/home';
                
                const info = new OAuthInfo({
                    appId: config.authentication.appId,
                    portalUrl: loginPortalUrl,
                    popup: true, // Use popup flow for better session persistence
                    flowType: "auto",
                    preserveUrlHash: true
                });

                signInButton.addEventListener("click", signInOrOut);
                navigationUser.addEventListener("click", signInOrOut);
                
                esriId.registerOAuthInfos([info]);
                esriId.useSignInPage = false;
                esriId.tokenDuration = 20160;
                    
                    checkSignIn();
            }

            // Function to store authenticated user info in localStorage
            function storeAuthenticatedUser(username) {
                if (username) {
                    localStorage.setItem('erdb_authenticated_user', username);
                    localStorage.setItem('erdb_auth_timestamp', Date.now().toString());
                    console.log(`Stored authenticated user: ${username}`);
                } else {
                    localStorage.removeItem('erdb_authenticated_user');
                    localStorage.removeItem('erdb_auth_timestamp');
                    console.log('Cleared authenticated user storage');
                }
            }

            // Enhanced function to check the current sign in status and query the portal if signed in.
            function checkSignIn() {
                console.log('🔍 Checking sign-in status...');
                
                // First try to find existing credentials
                const existingCredentials = esriId.findCredential(portalUrl + "/sharing");
                if (existingCredentials) {
                    console.log('✅ Found existing credentials - user should be authenticated');
                }
                
                esriId
                    .checkSignInStatus(portalUrl + "/sharing")
                    .then(() => {
                        console.log('✅ User is signed in - loading portal...');
                        
                        // NOTE: Don't update UI elements directly here - use centralized function instead
                        
                        const portal = new Portal({
                            authMode: "immediate",
                            url: portalUrl // Use the base portal URL for API calls, not the login URL
                        });
                        
                        // Load the portal, display the name and username, then call the query items function.
                        portal.load().then(() => {
                            console.log('🌐 Portal loaded successfully');
                            console.log('👤 User info:', {
                                username: portal.user.username,
                                fullName: portal.user.fullName,
                                email: portal.user.email
                            });
                            
                            // Use centralized UI state management
                            const userInfo = {
                                username: portal.user.username,
                                fullName: portal.user.fullName,
                                email: portal.user.email,
                                thumbnailUrl: portal.user.thumbnailUrl
                            };
                            
                            updateUIAuthenticationState(true, userInfo);
                            
                            if (navLogo) {
                            navLogo.description = config.app.signOutDescription;
                            }
                            
                            // Store authenticated user info in localStorage
                            storeAuthenticatedUser(userInfo.username);
                            
                            queryItems(portal);
                            
                            // Show welcome notification
                            showNotification(`Welcome back, ${userInfo.fullName || userInfo.username}!`, 'success', 4000);
                            
                        }).catch(error => {
                            console.error('❌ Error loading portal:', error);
                            console.error('Portal error details:', {
                                message: error.message,
                                stack: error.stack,
                                name: error.name
                            });
                            showNotification('Error loading user profile', 'error', 3000);
                            updateUIAuthenticationState(false);
                        });
                    })
                    .catch((error) => {
                        console.log('ℹ️ User not signed in:', error.message || 'No active session');
                        console.log('Authentication error details:', {
                            message: error.message,
                            name: error.name,
                            type: typeof error
                        });
                        
                        // Use centralized UI state management
                        updateUIAuthenticationState(false);
                        
                        if (navLogo) {
                            navLogo.description = config.app.signInDescription;
                        }
                        
                        // Clear stored authentication
                        storeAuthenticatedUser(null);
                    });
            }

            // Configuration access control
            const AUTHORIZED_CONFIG_USERS = ['afolvida'];
            // Initialize global currentUser to avoid scope/timing issues
            if (typeof window !== 'undefined' && typeof window.currentUser === 'undefined') {
                window.currentUser = null;
            }
            let currentUser = window.currentUser;
            
            // Enhanced UI state management with robust error handling
            function updateUIAuthenticationState(isAuthenticated, userInfo = null) {
                console.log('🎯 Updating UI authentication state:', { isAuthenticated, userInfo });
                
                const signInButton = document.getElementById("sign-in-button");
                const navigationUser = document.getElementById("nav-user");
                
                if (isAuthenticated && userInfo) {
                    // User is authenticated - show user info, hide sign in
                    if (navigationUser) {
                        navigationUser.hidden = false;
                        navigationUser.style.display = 'flex';
                        navigationUser.fullName = userInfo.fullName;
                        navigationUser.username = userInfo.username;
                        
                        if (userInfo.thumbnailUrl) {
                            navigationUser.thumbnail = userInfo.thumbnailUrl;
                        }
                        console.log('✅ Navigation user updated with:', userInfo);
                    } else {
                        console.warn('⚠️ Navigation user element not found');
                    }
                    
                    if (signInButton) {
                        signInButton.hidden = true;
                        signInButton.style.display = 'none';
                        console.log('✅ Sign-in button hidden');
                    } else {
                        console.warn('⚠️ Sign-in button element not found');
                    }
                    
                    // Update current user for config access - with validation
                    if (userInfo.username) {
                        currentUser = userInfo.username;
                        window.currentUser = currentUser;
                        console.log('✅ Current user set to:', currentUser);
                        updateConfigButtonVisibility();
                        
                        // Force config button visibility update after small delay to ensure DOM is ready
                        setTimeout(() => {
                            updateConfigButtonVisibility();
                        }, 100);
                    } else {
                        console.warn('⚠️ User info missing username');
                    }
                    
                } else {
                    // User is not authenticated - show sign in, hide user info
                    if (signInButton) {
                        signInButton.hidden = false;
                        signInButton.style.display = 'inline-flex';
                        console.log('✅ Sign-in button shown');
                    }
                    
                    if (navigationUser) {
                        navigationUser.hidden = true;
                        navigationUser.style.display = 'none';
                        console.log('✅ Navigation user hidden');
                    }
                    
                    // Clear current user
                    currentUser = null;
                    window.currentUser = currentUser;
                    console.log('✅ Current user cleared');
                    updateConfigButtonVisibility();
                }
                
                // Debug: Log final UI state
                console.log('🎯 Final UI state:', {
                    signInButtonVisible: signInButton ? !signInButton.hidden : 'element not found',
                    navigationUserVisible: navigationUser ? !navigationUser.hidden : 'element not found',
                    currentUser: currentUser
                });
            }
            // Ensure the UI updater is available globally for modules/callbacks
            if (typeof window !== 'undefined' && typeof window.updateUIAuthenticationState !== 'function') {
                window.updateUIAuthenticationState = updateUIAuthenticationState;
            }
            
            function updateConfigButtonVisibility() {
                const configButton = document.getElementById('config-button');
                console.log('🔧 Updating config button visibility...');
                console.log('Config button element found:', !!configButton);
                console.log('Current user:', currentUser);
                console.log('Authorized users:', AUTHORIZED_CONFIG_USERS);
                
                if (!configButton) {
                    console.error('❌ Config button element not found in DOM');
                    return;
                }
                
                if (currentUser) {
                    const isAuthorized = AUTHORIZED_CONFIG_USERS.includes(currentUser.toLowerCase());
                    console.log(`🔍 Authorization check for ${currentUser}: ${isAuthorized}`);
                    
                    if (isAuthorized) {
                        configButton.style.display = 'inline-flex'; // Use inline-flex for better alignment
                        configButton.title = `Configuration (Admin: ${currentUser})`;
                        console.log(`✅ Configuration access GRANTED for user: ${currentUser}`);
                        
                        // Only show notification once per session to avoid spam
                        if (!window.configAccessNotificationShown) {
                            showNotification(`🔧 Configuration access enabled for admin ${currentUser}`, 'success', 3000);
                            window.configAccessNotificationShown = true;
                        }
                    } else {
                        configButton.style.display = 'none';
                        console.log(`❌ Configuration access DENIED for user: ${currentUser} (not in authorized list)`);
                    }
                } else {
                    configButton.style.display = 'none';
                    console.log('🔒 Configuration button hidden - no user authenticated');
                }
                
                // Debug: Final button state
                console.log('🔧 Config button final state:', {
                    display: configButton.style.display,
                    visible: configButton.style.display !== 'none',
                    title: configButton.title
                });
            }
            // Export globally for external modules/callbacks
            if (typeof window !== 'undefined' && typeof window.updateConfigButtonVisibility !== 'function') {
                window.updateConfigButtonVisibility = updateConfigButtonVisibility;
            }

            // Query items function for portal integration
            function queryItems(portal) {
                try {
                    const queryParams = {
                        query: "owner:" + portal.user.username,
                        sortField: "modified",
                        sortOrder: "desc",
                        num: 20
                    };

                    portal.queryItems(queryParams).then((response) => {
                        console.log("User items:", response.results);
                        
                        // Update item gallery if it exists
                        const itemGallery = document.getElementById("item-gallery");
                        if (itemGallery && response.results.length > 0) {
                            itemGallery.innerHTML = '<h3>Recent Items</h3>';
                            response.results.slice(0, 5).forEach(item => {
                                const itemDiv = document.createElement('div');
                                itemDiv.style.cssText = 'padding: 8px; border-bottom: 1px solid #eee;';
                                itemDiv.innerHTML = `
                                    <strong>${SecurityUtils.validateAndSanitize.text(item.title)}</strong><br>
                                    <small>${SecurityUtils.validateAndSanitize.text(item.type)} - ${new Date(item.modified).toLocaleDateString()}</small>
                                `;
                                itemGallery.appendChild(itemDiv);
                            });
                        }
                        
                        showNotification(`Found ${response.results.length} items in your portal`, 'info', 3000);
                    }).catch((error) => {
                        console.error("Error querying portal items:", error);
                        showNotification("Could not load portal items", 'warning', 3000);
                    });
                } catch (error) {
                    console.error("Error in queryItems function:", error);
                }
            }

            // Enhanced function to sign in or out of the portal
            function signInOrOut() {
                console.log('🔄 Sign in/out button clicked...');
                
                esriId
                    .checkSignInStatus(portalUrl + "/sharing")
                    .then(() => {
                        // User is already signed in - sign them out
                        console.log('👋 Signing out user...');
                        showNotification('Signing out...', 'info', 2000);
                        
                        esriId.destroyCredentials();
                        currentUser = null;
                        window.currentUser = currentUser;
                        
                        // Clear stored authentication
                        storeAuthenticatedUser(null);
                        
                        // Update UI immediately
                        updateUIAuthenticationState(false);
                        
                        // Reload page to ensure clean state
                        setTimeout(() => {
                		window.location.reload();
                        }, 1000);
                    })
                    .catch(() => {
                        // User is not signed in - initiate sign in
                        console.log('🔐 Initiating sign in process...');
                        showNotification('Opening DENR Portal sign-in...', 'info', 3000);
                        
                        // Show loading state
                        const signInButton = document.getElementById("sign-in-button");
                        if (signInButton) {
                            signInButton.loading = true;
                            signInButton.disabled = true;
                        }
                        
                        esriId
                            .getCredential(info.portalUrl + "/sharing", {
                                oAuthPopupConfirmation: false, // Skip confirmation dialog
                                signal: AbortSignal.timeout(60000) // 60 second timeout for redirect flow
                            })
                            .then(() => {
                                console.log('✅ Login successful - checking user status...');
                                showNotification('Login successful! Loading your profile...', 'success', 3000);
                                
                                // Reset button state
                                if (signInButton) {
                                    signInButton.loading = false;
                                    signInButton.disabled = false;
                                }
                                
                                // Check sign in status to update UI
                                checkSignIn();
                            })
                            .catch((error) => {
                                console.error('❌ Login failed:', error);
                                
                                // Reset button state
                                if (signInButton) {
                                    signInButton.loading = false;
                                    signInButton.disabled = false;
                                }
                                
                                if (error.name === 'AbortError') {
                                    showNotification('Login timed out. Please try again.', 'warning', 5000);
                                } else if (error.message.includes('User cancelled')) {
                                    showNotification('Login cancelled by user.', 'info', 3000);
                                } else {
                                    showNotification('Login failed. Please try again or contact support.', 'error', 5000);
                                }
                            });
                    });
            }

        
            // User filter widget removed for simplified interface
            
            // Function to add clustering toggle for large datasets
            function addClusteringToggle(layer, featureCount) {
                const toggleButton = document.createElement('calcite-button');
                toggleButton.innerHTML = '🔗 Toggle Clustering';
                toggleButton.kind = 'outline';
                toggleButton.scale = 's';
                toggleButton.title = `Toggle clustering for ${featureCount} features`;
                
                let clusteringEnabled = true;
                
                toggleButton.addEventListener('click', () => {
                    if (clusteringEnabled) {
                        // Disable clustering - show actual geometries
                        layer.featureReduction = null;
                        toggleButton.innerHTML = '🔗 Enable Clustering';
                        toggleButton.kind = 'neutral';
                        showNotification('Clustering disabled - showing actual feature shapes', 'info', 2000);
                        clusteringEnabled = false;
                    } else {
                        // Enable clustering
                        layer.featureReduction = {
                            type: "cluster",
                            clusterRadius: featureCount > 5000 ? "150px" : "100px",
                            clusterMinSize: "24px",
                            clusterMaxSize: "60px",
                            labelingInfo: [{
                                deconflictionStrategy: "none",
                                labelExpressionInfo: {
                                    expression: "Text($feature.cluster_count, '#,###')"
                                },
                                symbol: {
                                    type: "text",
                                    color: "#004c73",
                                    font: {
                                        weight: "bold",
                                        family: "Noto Sans",
                                        size: "12px"
                                    }
                                },
                                labelPlacement: "center-center"
                            }]
                        };
                        toggleButton.innerHTML = '🔗 Disable Clustering';
                        toggleButton.kind = 'outline';
                        showNotification('Clustering enabled - features grouped for performance', 'info', 2000);
                        clusteringEnabled = true;
                    }
                });
                
                // Add button to map UI
                view.ui.add(toggleButton, "top-right");
            }
            
                // Set global reference to feature layer for filtering
                globalFeatureLayer = featureLayer;
                // Store view reference for quick actions
                if (featureLayer) {
                    featureLayer.view = view;
                }

                // Watch for when the feature layer view is ready
            view.whenLayerView(featureLayer).then((layerView) => {
                    globalFeatureLayerView = layerView;
                    
                    // Populate filter dropdowns when layer is ready
                    window.populateFilterDropdowns();
                    
                    console.log('Feature layer view ready for filtering');
                });

            }); // End of require callback

        } // End of initializeArcGISComponents function

        // Global configuration access function (accessible from HTML onclick)
        window.openConfiguration = function() {
            // Get current user from the application context
            const configButton = document.getElementById('config-button');
            if (!configButton || configButton.style.display === 'none') {
                console.log('🔧 Configuration access denied - button not visible');
                console.log('Current user:', currentUser);
                console.log('Authorized users:', AUTHORIZED_CONFIG_USERS);
                
                if (currentUser) {
                    showNotification(`Configuration access not available for user: ${currentUser}`, 'warning', 3000);
                } else {
                    showNotification('Please sign in to access configuration', 'warning', 3000);
                }
                return;
            }
            
            // If button is visible, user is authorized
            console.log('✅ Opening configuration for user:', currentUser);
            window.open('config.html', '_blank');
        };
        
        // Global function to manually trigger auth state check (for debugging)
        window.debugAuthState = function() {
            console.log('🔍 Manual authentication state debug:');
            console.log('Current user:', currentUser);
            console.log('Authorized users:', AUTHORIZED_CONFIG_USERS);
            
            const signInButton = document.getElementById("sign-in-button");
            const navigationUser = document.getElementById("nav-user");
            const configButton = document.getElementById('config-button');
            
            console.log('UI Elements state:', {
                signInButton: signInButton ? { hidden: signInButton.hidden, display: signInButton.style.display } : 'not found',
                navigationUser: navigationUser ? { hidden: navigationUser.hidden, display: navigationUser.style.display, username: navigationUser.username } : 'not found',
                configButton: configButton ? { display: configButton.style.display, title: configButton.title } : 'not found'
            });
            
            // Check localStorage
            const storedUser = localStorage.getItem('erdb_authenticated_user');
            const storedTimestamp = localStorage.getItem('erdb_auth_timestamp');
            console.log('LocalStorage auth:', { user: storedUser, timestamp: storedTimestamp });
            
            // Force auth state recovery
            if (storedUser && !currentUser) {
                console.log('🔧 Forcing authentication state recovery...');
                forceAuthenticationStateRecovery();
            }
        };

        // Global Calcite notification function (accessible from config access)
        window.showNotification = function(message, type = 'info', duration = 5000) {
            // Create notification container if it doesn't exist
            let container = document.querySelector('.calcite-notification-container');
            if (!container) {
                container = document.createElement('div');
                container.className = 'calcite-notification-container';
                document.body.appendChild(container);
            }

            // Map types to Calcite notice kinds
            const kindMap = {
                'info': 'brand',
                'success': 'success', 
                'warning': 'warning',
                'error': 'danger'
            };

            // Map types to icons
            const iconMap = {
                'info': 'information',
                'success': 'check-circle',
                'warning': 'exclamation-mark-triangle',
                'error': 'exclamation-mark-triangle-f'
            };

            // Create Calcite notice
            const notice = document.createElement('calcite-notice');
            notice.setAttribute('open', '');
            notice.setAttribute('kind', kindMap[type] || 'brand');
            notice.setAttribute('icon', iconMap[type] || 'information');
            notice.setAttribute('auto-close', '');
            notice.setAttribute('auto-close-duration', duration.toString());

            // Create notice message
            const noticeMessage = document.createElement('div');
            noticeMessage.setAttribute('slot', 'message');
            // Simple sanitization for global function
            const sanitizedMessage = String(message).replace(/[<>\"'&]/g, '');
            noticeMessage.textContent = sanitizedMessage;
            notice.appendChild(noticeMessage);

            container.appendChild(notice);

            // Auto-remove from DOM after animation
            setTimeout(() => {
                if (notice.parentNode) {
                    notice.remove();
                }
            }, duration + 500);
            
            return notice;
        };

        // Global variables for filtering
        let globalFeatureLayerView = null;
        let globalFeatureLayer = null;
        let globalFilterValues = {
            office_level_2: '',
            region: ''
        };

        // Global function to populate filter dropdowns with unique values
        window.populateFilterDropdowns = function() {
            if (!globalFeatureLayer) return;

            globalFeatureLayer.queryFeatures().then((results) => {
                const features = results.features;
                
                // Get unique values for Office Level 2
                const officeLevels = [...new Set(features
                    .map(f => f.attributes.office_level_2)
                    .filter(val => val && val.trim() !== '')
                )].sort();

                // Get unique values for Region
                const regions = [...new Set(features
                    .map(f => f.attributes.region)
                    .filter(val => val && val.trim() !== '')
                )].sort();

                // Populate Office Level 2 dropdown
                const officeLevelSelect = document.getElementById('office-level-2-filter');
                if (officeLevelSelect) {
                    officeLevelSelect.innerHTML = '<calcite-option value="">All Office Levels</calcite-option>';
                    officeLevels.forEach(level => {
                        const option = document.createElement('calcite-option');
                        option.value = level;
                        option.textContent = level;
                        officeLevelSelect.appendChild(option);
                    });
                }

                // Populate Region dropdown
                const regionSelect = document.getElementById('region-filter');
                if (regionSelect) {
                    regionSelect.innerHTML = '<calcite-option value="">All Regions</calcite-option>';
                    regions.forEach(region => {
                        const option = document.createElement('calcite-option');
                        option.value = region;
                        option.textContent = region;
                        regionSelect.appendChild(option);
                    });
                }

                console.log('Filter dropdowns populated:', {
                    officeLevels: officeLevels.length,
                    regions: regions.length
                });
            }).catch((error) => {
                console.error('Error populating filter dropdowns:', error);
            });
        };

        // Global filter functions (accessible from HTML onclick)
        window.toggleFilterPanel = function() {
            const filterShellPanel = document.getElementById('filter-shell-panel');
            if (filterShellPanel) {
                const isCollapsed = filterShellPanel.hasAttribute('collapsed');
                
                if (isCollapsed) {
                    filterShellPanel.removeAttribute('collapsed');
                    // Panel is being opened, update filter status
                    window.updateFilterStatus();
                } else {
                    filterShellPanel.setAttribute('collapsed', '');
                }
            }
        };

        window.applyFilters = function() {
            const officeLevelSelect = document.getElementById('office-level-2-filter');
            const regionSelect = document.getElementById('region-filter');
            
            if (officeLevelSelect && regionSelect) {
                globalFilterValues.office_level_2 = officeLevelSelect.value;
                globalFilterValues.region = regionSelect.value;
                
                window.applyLayerFilters();
                
                showNotification('Filters applied successfully!', 'success', 2000);
            }
        };

        window.clearFilters = function() {
            const officeLevelSelect = document.getElementById('office-level-2-filter');
            const regionSelect = document.getElementById('region-filter');
            
            if (officeLevelSelect && regionSelect) {
                officeLevelSelect.value = '';
                regionSelect.value = '';
                globalFilterValues.office_level_2 = '';
                globalFilterValues.region = '';
                
                window.applyLayerFilters();
                
                showNotification('Filters cleared - showing all buildings', 'info', 2000);
            }
        };

        // Global filter utility functions
        window.applyLayerFilters = function() {
            if (!globalFeatureLayerView) return;

            let whereClause = "1=1"; // Default to show all
            const conditions = [];

            if (globalFilterValues.office_level_2) {
                conditions.push(`office_level_2 = '${globalFilterValues.office_level_2.replace(/'/g, "''")}'`);
            }

            if (globalFilterValues.region) {
                conditions.push(`region = '${globalFilterValues.region.replace(/'/g, "''")}'`);
            }

            if (conditions.length > 0) {
                whereClause = conditions.join(' AND ');
            }

            globalFeatureLayerView.filter = {
                where: whereClause
            };

            // Update status
            window.updateFilterStatus();
            console.log('Filter applied:', whereClause);
        };

        window.updateFilterStatus = function() {
            const statusElement = document.getElementById('filter-status');
            const statusNotice = document.getElementById('filter-status-notice');
            const filterStatusChip = document.getElementById('filter-status-chip');
            
            if (!statusElement || !statusNotice) return;

            if (globalFilterValues.office_level_2 || globalFilterValues.region) {
                const activeFilters = [];
                if (globalFilterValues.office_level_2) activeFilters.push(`Office: ${globalFilterValues.office_level_2}`);
                if (globalFilterValues.region) activeFilters.push(`Region: ${globalFilterValues.region}`);
                
                const filterText = `Active filters: ${activeFilters.join(', ')}`;
                statusElement.textContent = filterText;
                statusNotice.setAttribute('kind', 'success');
                statusNotice.setAttribute('icon', 'check-circle');
                
                // Update status chip
                if (filterStatusChip) {
                    filterStatusChip.innerHTML = `<calcite-icon icon="filter" slot="icon"></calcite-icon>Filtered`;
                    filterStatusChip.setAttribute('kind', 'brand');
                }
            } else {
                statusElement.textContent = 'No filters active - showing all buildings';
                statusNotice.setAttribute('kind', 'info');
                statusNotice.setAttribute('icon', 'information');
                
                // Update status chip
                if (filterStatusChip) {
                    filterStatusChip.innerHTML = `<calcite-icon icon="filter" slot="icon"></calcite-icon>No filters`;
                    filterStatusChip.setAttribute('kind', 'neutral');
                }
            }
        };

        // Enhanced layer status update function
        window.updateLayerStatus = function(featureCount, layerName) {
            const statusCard = document.getElementById('layer-status-card');
            const statusSubtitle = document.getElementById('layer-status-subtitle');
            const featureCountChip = document.getElementById('feature-count-chip');
            
            if (statusCard && statusSubtitle && featureCountChip) {
                statusSubtitle.textContent = layerName || 'DENR Administrative Buildings';
                featureCountChip.innerHTML = `<calcite-icon icon="number" slot="icon"></calcite-icon>Features: ${featureCount || 0}`;
                
                // Show the status card
                statusCard.style.display = 'block';
                
                // Auto-hide after 5 seconds unless there are active filters
                if (!globalFilterValues.office_level_2 && !globalFilterValues.region) {
                    setTimeout(() => {
                        if (statusCard.style.display !== 'none') {
                            statusCard.style.display = 'none';
                        }
                    }, 5000);
                }
            }
        };

        // Enhanced Quick Action Functions
        window.zoomToAllBuildings = function() {
            if (globalFeatureLayer && globalFeatureLayer.fullExtent) {
                const view = globalFeatureLayer.view;
                if (view) {
                    view.goTo(globalFeatureLayer.fullExtent.expand(1.2)).then(() => {
                        showNotification('Zoomed to show all buildings', 'success', 2000);
                    });
                }
            }
        };

        window.refreshLayerData = function() {
            if (globalFeatureLayer) {
                const loadingOverlay = showLoadingOverlay(document.getElementById('mapContainer'), 'Refreshing building data...');
                
                globalFeatureLayer.refresh().then(() => {
                    hideLoadingOverlay(loadingOverlay);
                    showNotification('Building data refreshed successfully', 'success', 3000);
                    
                    // Repopulate filter dropdowns with fresh data
                    window.populateFilterDropdowns();
                }).catch((error) => {
                    hideLoadingOverlay(loadingOverlay);
                    showNotification('Failed to refresh data: ' + error.message, 'error', 5000);
                });
            }
        };

        window.exportVisibleFeatures = function() {
            showNotification('Export feature coming soon!', 'info', 3000);
        };

        // Enhanced FAB interaction
        window.addEventListener('DOMContentLoaded', function() {
            const fab = document.getElementById('quick-actions-fab');
            const popover = document.getElementById('quick-actions-popover');
            
            if (fab && popover) {
                fab.addEventListener('click', () => {
                    popover.open = !popover.open;
                });
            }
        });

        // Force authentication state recovery on page load
        function forceAuthenticationStateRecovery() {
            console.log('🔄 Force authentication state recovery...');
            
            // Check localStorage for stored authentication
            const storedUser = localStorage.getItem('erdb_authenticated_user');
            const storedTimestamp = localStorage.getItem('erdb_auth_timestamp');
            
            if (storedUser && storedTimestamp) {
                // Check if auth is still valid (within 24 hours)
                const timeDiff = Date.now() - parseInt(storedTimestamp);
                const isValid = timeDiff < (24 * 60 * 60 * 1000);
                
                if (isValid) {
                    console.log(`🔍 Found valid stored auth for user: ${storedUser}`);
                    
                    // Force UI update with stored auth
                    const userInfo = {
                        username: storedUser,
                        fullName: storedUser, // Fallback if full name not available
                        email: null
                    };
                    
                    currentUser = storedUser;
                    window.currentUser = currentUser;
                    if (typeof window.updateUIAuthenticationState === 'function') {
                        window.updateUIAuthenticationState(true, userInfo);
                    } else {
                        console.warn('updateUIAuthenticationState not ready during auth recovery');
                    }
                    console.log('✅ Authentication UI state recovered from localStorage');
                } else {
                    console.log('⚠️ Stored authentication expired, clearing...');
                    localStorage.removeItem('erdb_authenticated_user');
                    localStorage.removeItem('erdb_auth_timestamp');
                }
            } else {
                console.log('ℹ️ No stored authentication found');
            }
        }
        
        // Enhanced application initialization with auth state recovery
        function initializeWithAuthRecovery() {
            console.log('🚀 Initializing application with auth recovery...');
            
            // First, try to recover authentication state from localStorage
            forceAuthenticationStateRecovery();
            
            // Then proceed with normal initialization
            initializeApplication();
            
            // Add a delayed auth state validation after everything loads
            setTimeout(() => {
                console.log('🔍 Post-initialization auth state validation...');
                
                // If we still don't have proper UI state, try to force it
                const signInButton = document.getElementById("sign-in-button");
                const navigationUser = document.getElementById("nav-user");
                const configButton = document.getElementById('config-button');
                
                console.log('🔍 Current UI elements state:', {
                    signInButton: signInButton ? !signInButton.hidden : 'not found',
                    navigationUser: navigationUser ? !navigationUser.hidden : 'not found',
                    configButton: configButton ? configButton.style.display : 'not found',
                    currentUser: currentUser
                });
                
                // If we have a currentUser but UI doesn't reflect it, fix it
                if (currentUser && signInButton && !signInButton.hidden) {
                    console.log('🔧 Fixing authentication UI state mismatch...');
                    const userInfo = {
                        username: currentUser,
                        fullName: currentUser,
                        email: null
                    };
                    if (typeof window.updateUIAuthenticationState === 'function') {
                        window.updateUIAuthenticationState(true, userInfo);
                    } else {
                        console.warn('updateUIAuthenticationState not ready during post-init validation');
                    }
                }
            }, 2000); // Wait 2 seconds for everything to initialize
        }

        // Export init functions to window for safety
        if (typeof window !== 'undefined') {
            window.forceAuthenticationStateRecovery = forceAuthenticationStateRecovery;
            window.initializeWithAuthRecovery = initializeWithAuthRecovery;
        }

        // Initialize application when DOM is ready - direct approach
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                initializeWithAuthRecovery();
            });
        } else {
            initializeWithAuthRecovery();
        }

    </script>
</head>
<body>
<!-- Main title now handled by calcite-navigation-logo -->
<div style="display: none;">
    <h1 id="main-title">DENR Control Map</h1>
</div>


<calcite-shell content-behind>
    <!-- Enhanced Navigation Header -->
    <calcite-navigation slot="header">
        <calcite-navigation-logo
                id="nav-logo"
                slot="logo"
                heading="DENR Infrastructure Map"
                description="Administrative Buildings Control System"
        ></calcite-navigation-logo>
         <calcite-button id="sign-in-button" slot="user" kind="brand" icon-start="sign-in">
            Sign in to DENR Portal
        </calcite-button>
        <calcite-navigation-user hidden id="nav-user" slot="user"> </calcite-navigation-user>
        <div slot="user" style="display: flex; align-items: center; gap: 0.5rem;">
            <calcite-action id="config-button" icon="gear" text="Configuration" onclick="openConfiguration()" style="display: none;">
                <calcite-tooltip slot="tooltip" placement="bottom">Administrative configuration (Admin only)</calcite-tooltip>
            </calcite-action>
            <calcite-action id="filter-button" icon="filter" text="Filter Buildings" onclick="toggleFilterPanel()">
                <calcite-tooltip slot="tooltip" placement="bottom">Open building filters panel</calcite-tooltip>
            </calcite-action>
        </div>
      </calcite-navigation>

    <!-- Collapsible Filter Panel -->
    <calcite-shell-panel slot="panel-start" id="filter-shell-panel" width-scale="m" collapsed>
        <calcite-panel heading="🏢 Building Filters" description="Filter buildings by office level and region">
            <calcite-action slot="header-actions-end" icon="x" text="Close Panel" onclick="toggleFilterPanel()"></calcite-action>
            
            <!-- Filter Content Card -->
            <calcite-card>
                <div slot="title">Filter Criteria</div>
                <div slot="subtitle">Select filters to narrow down building results</div>
                
                <!-- Office Level 2 Filter -->
                <calcite-label style="margin-bottom: 1rem;">
                    Office Level 2
                    <calcite-select id="office-level-2-filter" width="full">
                        <calcite-option value="">All Office Levels</calcite-option>
                    </calcite-select>
                </calcite-label>

                <!-- Region Filter -->
                <calcite-label style="margin-bottom: 1.5rem;">
                    Region
                    <calcite-select id="region-filter" width="full">
                        <calcite-option value="">All Regions</calcite-option>
                    </calcite-select>
                </calcite-label>

                <!-- Action Buttons -->
                <div style="display: flex; gap: 0.5rem;">
                    <calcite-button id="apply-filter" onclick="applyFilters()" width="half" kind="brand">
                        <calcite-icon icon="filter" slot="icon-start"></calcite-icon>
                        Apply Filters
                    </calcite-button>
                    <calcite-button id="clear-filter" onclick="clearFilters()" appearance="outline" width="half">
                        <calcite-icon icon="trash" slot="icon-start"></calcite-icon>
                        Clear
                    </calcite-button>
    </div>

                <!-- Filter Status Notice -->
                <calcite-notice id="filter-status-notice" kind="info" icon="information" style="margin-top: 1rem;">
                    <div slot="message" id="filter-status">No filters active - showing all buildings</div>
                </calcite-notice>
            </calcite-card>
        </calcite-panel>
    </calcite-shell-panel>

    <!-- Hidden Item Panel for Portal Integration -->
    <calcite-panel id="item-panel" hidden>
        <div id="item-gallery"></div>
    </calcite-panel>

    <!-- Main Map Container -->
    <div id="mapContainer"></div>

    <!-- Layer Status Indicator -->
    <calcite-card id="layer-status-card" style="position: absolute; bottom: 24px; left: 24px; z-index: 1000; max-width: 300px; display: none;">
        <div slot="title">Layer Status</div>
        <div slot="subtitle" id="layer-status-subtitle">Loading...</div>
        <div id="layer-status-content">
            <calcite-chip id="feature-count-chip" kind="neutral" scale="s">
                <calcite-icon icon="number" slot="icon"></calcite-icon>
                Features: 0
            </calcite-chip>
            <calcite-chip id="filter-status-chip" kind="neutral" scale="s" style="margin-left: 0.5rem;">
                <calcite-icon icon="filter" slot="icon"></calcite-icon>
                No filters
            </calcite-chip>
        </div>
    </calcite-card>

    <!-- Floating Action Button for Quick Actions -->
    <calcite-fab id="quick-actions-fab" icon="plus" text="Quick Actions" style="position: absolute; bottom: 24px; right: 24px; z-index: 1000;"></calcite-fab>

    <!-- Quick Actions Popover -->
    <calcite-popover id="quick-actions-popover" reference-element="quick-actions-fab" placement="top-end" auto-close>
        <div style="padding: 0.5rem;">
            <calcite-action text="Zoom to All Buildings" icon="magnifying-glass-plus" onclick="zoomToAllBuildings()">
                <calcite-tooltip slot="tooltip" placement="left">Zoom map to show all buildings</calcite-tooltip>
            </calcite-action>
            <calcite-action text="Refresh Data" icon="refresh" onclick="refreshLayerData()">
                <calcite-tooltip slot="tooltip" placement="left">Refresh building data from server</calcite-tooltip>
            </calcite-action>
            <calcite-action text="Export Visible" icon="download" onclick="exportVisibleFeatures()">
                <calcite-tooltip slot="tooltip" placement="left">Export currently visible buildings</calcite-tooltip>
            </calcite-action>
    </div>
    </calcite-popover>

</calcite-shell>
</body>
</html>

