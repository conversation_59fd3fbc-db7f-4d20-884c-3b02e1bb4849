/**
 * Security Utilities Module
 * Provides encryption, validation, and sanitization functions
 * Used by both main application and configuration interface
 */

const SecurityUtils = {
    /**
     * Encrypt sensitive data using Web Crypto API
     * @param {string} data - Data to encrypt
     * @param {string} password - Password for encryption
     * @returns {Promise<Object>} Encrypted data object
     */
    async encryptData(data, password) {
        try {
            const encoder = new TextEncoder();
            const salt = crypto.getRandomValues(new Uint8Array(16));
            const iv = crypto.getRandomValues(new Uint8Array(12));
            
            const key = await crypto.subtle.importKey(
                'raw',
                encoder.encode(password),
                { name: 'PBKDF2' },
                false,
                ['deriveKey']
            );
            
            const derivedKey = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: salt,
                    iterations: 100000,
                    hash: 'SHA-256'
                },
                key,
                { name: 'AES-GCM', length: 256 },
                false,
                ['encrypt']
            );
            
            const encrypted = await crypto.subtle.encrypt(
                { name: 'AES-GCM', iv: iv },
                derivedKey,
                encoder.encode(data)
            );
            
            return {
                encrypted: Array.from(new Uint8Array(encrypted)),
                salt: Array.from(salt),
                iv: Array.from(iv)
            };
        } catch (error) {
            console.error('Encryption error:', error);
            throw new Error('Failed to encrypt data');
        }
    },
    
    /**
     * Decrypt sensitive data
     * @param {Object} encryptedData - Encrypted data object
     * @param {string} password - Password for decryption
     * @returns {Promise<string>} Decrypted data
     */
    async decryptData(encryptedData, password) {
        try {
            const encoder = new TextEncoder();
            const decoder = new TextDecoder();
            
            const key = await crypto.subtle.importKey(
                'raw',
                encoder.encode(password),
                { name: 'PBKDF2' },
                false,
                ['deriveKey']
            );
            
            const derivedKey = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: new Uint8Array(encryptedData.salt),
                    iterations: 100000,
                    hash: 'SHA-256'
                },
                key,
                { name: 'AES-GCM', length: 256 },
                false,
                ['decrypt']
            );
            
            const decrypted = await crypto.subtle.decrypt(
                { name: 'AES-GCM', iv: new Uint8Array(encryptedData.iv) },
                derivedKey,
                new Uint8Array(encryptedData.encrypted)
            );
            
            return decoder.decode(decrypted);
        } catch (error) {
            console.error('Decryption error:', error);
            throw new Error('Failed to decrypt data');
        }
    },
    
    /**
     * Input validation and sanitization utilities
     */
    validateAndSanitize: {
        /**
         * Validate and sanitize URL input
         * @param {string} input - URL to validate
         * @returns {string|null} Valid URL or null
         */
        url(input) {
            if (!input || typeof input !== 'string') return null;
            try {
                const url = new URL(input.trim());
                return ['http:', 'https:'].includes(url.protocol) ? url.href : null;
            } catch {
                return null;
            }
        },
        
        /**
         * Sanitize text input
         * @param {string} input - Text to sanitize
         * @param {number} maxLength - Maximum length
         * @returns {string} Sanitized text
         */
        text(input, maxLength = 1000) {
            if (!input || typeof input !== 'string') return '';
            return input.trim().substring(0, maxLength).replace(/[<>"'&]/g, '');
        },
        
        /**
         * Validate and sanitize numeric input
         * @param {any} input - Number to validate
         * @param {number} min - Minimum value
         * @param {number} max - Maximum value
         * @returns {number|null} Valid number or null
         */
        number(input, min = -Infinity, max = Infinity) {
            const num = parseFloat(input);
            return isNaN(num) ? null : Math.max(min, Math.min(max, num));
        },
        
        /**
         * Validate and sanitize coordinate input
         * @param {any} input - Coordinates to validate
         * @returns {Array|null} Valid coordinates or null
         */
        coordinates(input) {
            try {
                const coords = Array.isArray(input) ? input : JSON.parse(input);
                if (!Array.isArray(coords) || coords.length !== 2) return null;
                const [lng, lat] = coords.map(c => parseFloat(c));
                if (isNaN(lng) || isNaN(lat)) return null;
                return [Math.max(-180, Math.min(180, lng)), Math.max(-90, Math.min(90, lat))];
            } catch {
                return null;
            }
        }
    }
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecurityUtils;
}

// Make available globally for inline script use
if (typeof window !== 'undefined') {
    window.SecurityUtils = SecurityUtils;
}
