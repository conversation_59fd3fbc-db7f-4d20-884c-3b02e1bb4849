/**
 * Error Management and Memory Cleanup Module
 * Provides centralized error handling, logging, and memory management
 */

const ErrorManager = {
    errorLog: [],
    maxLogSize: 100,
    disposables: new Set(),
    memoryWatchers: new Map(),
    memoryMonitorInterval: null,
    
    /**
     * Initialize error manager
     */
    init() {
        this.setupGlobalErrorHandling();
        this.setupMemoryMonitoring();
        this.setupUnloadHandlers();
        console.log('🛡️ Error Manager initialized');
    },

    /**
     * Setup global error handling
     */
    setupGlobalErrorHandling() {
        // Handle uncaught JavaScript errors
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                timestamp: new Date().toISOString()
            });
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logError({
                type: 'Unhandled Promise Rejection',
                message: event.reason?.message || String(event.reason),
                stack: event.reason?.stack,
                timestamp: new Date().toISOString()
            });
        });

        // Handle ArcGIS API errors if available
        if (typeof require !== 'undefined') {
            require(['esri/core/Error'], (EsriError) => {
                console.log('🗺️ ArcGIS error handling enabled');
            });
        }
    },

    /**
     * Setup memory monitoring
     */
    setupMemoryMonitoring() {
        // Monitor memory usage if Performance API is available
        if (window.performance && window.performance.memory) {
            this.memoryMonitorInterval = setInterval(() => {
                const memory = window.performance.memory;
                const usage = {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                };

                // Warn if memory usage is high
                const usagePercent = (usage.used / usage.limit) * 100;
                if (usagePercent > 80) {
                    console.warn(`⚠️ High memory usage: ${usagePercent.toFixed(1)}%`);
                    this.suggestMemoryCleanup();
                }

                this.memoryWatchers.set('current', usage);
            }, 30000); // Check every 30 seconds
        }
    },

    /**
     * Setup page unload handlers for cleanup
     */
    setupUnloadHandlers() {
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        // Handle visibility change for mobile devices
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseNonCriticalOperations();
            } else {
                this.resumeOperations();
            }
        });
    },

    /**
     * Log error with details
     * @param {Object} errorInfo - Error information
     */
    logError(errorInfo) {
        // Add to internal log
        this.errorLog.push(errorInfo);
        
        // Trim log if it gets too large
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog = this.errorLog.slice(-this.maxLogSize);
        }

        // Console logging based on error type
        if (errorInfo.type === 'JavaScript Error') {
            console.error('🚨 JavaScript Error:', errorInfo.message, errorInfo);
        } else if (errorInfo.type === 'Unhandled Promise Rejection') {
            console.error('🚨 Promise Rejection:', errorInfo.message, errorInfo);
        } else {
            console.error('🚨 Application Error:', errorInfo);
        }

        // Show user notification for critical errors
        this.showUserErrorNotification(errorInfo);

        // Attempt recovery for known error types
        this.attemptErrorRecovery(errorInfo);
    },

    /**
     * Show user-friendly error notification
     * @param {Object} errorInfo - Error information
     */
    showUserErrorNotification(errorInfo) {
        // Only show notifications for errors that affect user experience
        const userRelevantErrors = [
            'feature layer',
            'authentication',
            'configuration',
            'portal',
            'network'
        ];

        const isUserRelevant = userRelevantErrors.some(keyword => 
            errorInfo.message.toLowerCase().includes(keyword)
        );

        if (isUserRelevant && typeof window.showNotification === 'function') {
            let userMessage = 'An error occurred. Please try again.';
            
            // Provide specific guidance based on error type
            if (errorInfo.message.includes('feature layer')) {
                userMessage = 'Unable to load map data. Please check your connection and refresh.';
            } else if (errorInfo.message.includes('authentication')) {
                userMessage = 'Authentication failed. Please try signing in again.';
            } else if (errorInfo.message.includes('network')) {
                userMessage = 'Network connection issue. Please check your internet connection.';
            }

            window.showNotification(userMessage, 'error', 8000);
        }
    },

    /**
     * Attempt automatic error recovery
     * @param {Object} errorInfo - Error information
     */
    attemptErrorRecovery(errorInfo) {
        // Recovery strategies for common errors
        if (errorInfo.message.includes('TypeError: Cannot read property') && 
            errorInfo.message.includes('null')) {
            console.log('🔧 Attempting DOM element recovery...');
            // Retry DOM operations after a short delay
            setTimeout(() => {
                if (typeof window.initializeApplication === 'function') {
                    console.log('🔄 Retrying application initialization...');
                    try {
                        window.initializeApplication();
                    } catch (retryError) {
                        console.error('Retry initialization failed:', retryError);
                    }
                }
            }, 1000);
        }

        if (errorInfo.message.includes('feature layer') || errorInfo.message.includes('FeatureServer')) {
            console.log('🔧 Attempting feature layer recovery...');
            // Try to reload feature layer with fallback URL
            setTimeout(() => {
                if (typeof window.reloadFeatureLayer === 'function') {
                    window.reloadFeatureLayer();
                }
            }, 2000);
        }
    },

    /**
     * Register a disposable resource for cleanup
     * @param {Object} resource - Resource to dispose
     * @param {Function} disposeFunc - Function to call for disposal
     */
    registerDisposable(resource, disposeFunc) {
        this.disposables.add({ resource, dispose: disposeFunc });
    },

    /**
     * Suggest memory cleanup
     */
    suggestMemoryCleanup() {
        console.log('🧹 Performing memory cleanup...');
        
        // Clear old error logs
        if (this.errorLog.length > 50) {
            this.errorLog = this.errorLog.slice(-50);
        }

        // Suggest garbage collection if available
        if (window.gc) {
            window.gc();
        }

        // Clean up cached data
        this.cleanupCachedData();
    },

    /**
     * Clean up cached data
     */
    cleanupCachedData() {
        // Clear old cached features if feature layer is available
        if (typeof window.globalFeatureLayer !== 'undefined' && 
            window.globalFeatureLayer?.queryFeatureCount) {
            console.log('🗑️ Clearing feature cache...');
        }

        // Clear old notifications
        const notifications = document.querySelectorAll('calcite-notice');
        notifications.forEach(notice => {
            if (notice.parentNode) {
                notice.remove();
            }
        });
    },

    /**
     * Pause non-critical operations
     */
    pauseNonCriticalOperations() {
        console.log('⏸️ Pausing non-critical operations (page hidden)');
        
        // Pause auto-refresh timers
        if (window.autoRefreshTimer) {
            clearInterval(window.autoRefreshTimer);
        }

        // Reduce map rendering frequency
        if (typeof window.globalFeatureLayer !== 'undefined') {
            // Reduce update frequency
        }
    },

    /**
     * Resume operations
     */
    resumeOperations() {
        console.log('▶️ Resuming operations (page visible)');
        
        // Resume timers and normal operation
        if (typeof window.restartAutoRefresh === 'function') {
            window.restartAutoRefresh();
        }
    },

    /**
     * Cleanup all resources
     */
    cleanup() {
        console.log('🧹 Cleaning up resources...');
        
        // Dispose all registered resources
        this.disposables.forEach(disposable => {
            try {
                disposable.dispose();
            } catch (error) {
                console.warn('Error disposing resource:', error);
            }
        });

        // Clear disposables set
        this.disposables.clear();

        // Clear memory monitoring interval
        if (this.memoryMonitorInterval) {
            clearInterval(this.memoryMonitorInterval);
            this.memoryMonitorInterval = null;
        }

        // Clear memory watchers
        this.memoryWatchers.clear();

        // Clear error log
        this.errorLog = [];

        console.log('✅ Cleanup completed');
    },

    /**
     * Get error statistics
     * @returns {Object} Error statistics
     */
    getErrorStats() {
        const stats = {
            totalErrors: this.errorLog.length,
            errorTypes: {},
            recentErrors: this.errorLog.slice(-10),
            memoryUsage: this.memoryWatchers.get('current')
        };

        // Count error types
        this.errorLog.forEach(error => {
            stats.errorTypes[error.type] = (stats.errorTypes[error.type] || 0) + 1;
        });

        return stats;
    },

    /**
     * Handle ArcGIS specific errors
     * @param {Object} esriError - ArcGIS error object
     */
    handleArcGISError(esriError) {
        const errorInfo = {
            type: 'ArcGIS Error',
            message: esriError.message || 'Unknown ArcGIS error',
            code: esriError.code,
            details: esriError.details,
            timestamp: new Date().toISOString()
        };

        this.logError(errorInfo);

        // Specific handling for common ArcGIS errors
        if (esriError.code === 'network-error') {
            if (typeof window.showNotification === 'function') {
                window.showNotification(
                    'Network connection lost. Retrying...', 
                    'warning', 
                    5000
                );
            }
        } else if (esriError.code === 'authentication-failed') {
            if (typeof window.showNotification === 'function') {
                window.showNotification(
                    'Authentication failed. Please sign in again.', 
                    'error', 
                    8000
                );
            }
        }
    }
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorManager;
}

// Make available globally
if (typeof window !== 'undefined') {
    window.ErrorManager = ErrorManager;
    
    // Auto-initialize if DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => ErrorManager.init());
    } else {
        ErrorManager.init();
    }
}
