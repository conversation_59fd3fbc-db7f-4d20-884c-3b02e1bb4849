/**
 * Application Configuration Management Module
 * Handles configuration loading, saving, validation, and field discovery
 * Shared between main application and configuration interface
 */

const AppConfig = {
    // Default configuration
    default: {
        app: {
            title: "DENR Control Map",
            description: "Administrative Buildings Control System",
            logoHeading: "DENR Infrastructure Map",
            signOutDescription: "Administrative Buildings Control System. To sign out, click on the logged in user button.",
            signInDescription: "Sign in to DENR Portal to access the administrative buildings database."
        },
        map: {
            center: [122, 12],
            zoom: 6,
            basemap: "topo",
            alternateBasemap: "satellite"
        },
        services: {
            portalUrl: "https://controlmap.denr.gov.ph/arcgis",
            featureLayerUrl: "https://controlmap.denr.gov.ph/server/rest/services/Hosted/survey123_698ecf476c9640b6a70fc48da500e50a_results/FeatureServer"
        },
        authentication: {
            appId: "5ybGb7GnJFt6V7Rd",
            requireAuth: false,
            authMode: "oauth", // "oauth", "token", "credentials"
            username: "",
            password: "",
            token: "",
            tokenExpiration: null,
            autoSignIn: false
        },
        layout: {
            mapHeight: "70%",
            tableHeight: "30%"
        },
        popupTemplate: {
            title: "{name_building}",
            fieldInfos: [
                // Administrative Information
                { fieldName: "office_level_1", label: "Office Level 1" },
                { fieldName: "office_level_2", label: "Office Level 2" },
                { fieldName: "region", label: "Region" },
                { fieldName: "bureau", label: "Bureau" },
                { fieldName: "penro", label: "PENRO" },
                { fieldName: "cenro", label: "CENRO" },
                
                // Building Information
                { fieldName: "name_building", label: "Building Name" },
                { fieldName: "facility_code", label: "Facility Code" },
                { fieldName: "facility_id", label: "Facility ID" },
                { fieldName: "building_purpose", label: "Building Purpose" },
                { fieldName: "building_purpose_others", label: "Other Purpose" },
                { fieldName: "year_constructed", label: "Year Constructed" },
                { fieldName: "floor_area", label: "Floor Area (sq.m)", format: { digitSeparator: true, places: 2 } },
                { fieldName: "number_floor", label: "Number of Floors" },
                { fieldName: "max_occupants", label: "Max Occupants", format: { digitSeparator: true } },
                { fieldName: "buildingvalue", label: "Building Value", format: { digitSeparator: true, places: 2 } },
                
                // Land Information
                { fieldName: "street_address", label: "Street Address" },
                { fieldName: "land_area", label: "Land Area (sq.m)", format: { digitSeparator: true, places: 2 } },
                { fieldName: "registered_owner", label: "Registered Owner" },
                { fieldName: "ownership_type", label: "Ownership Type" },
                { fieldName: "year_acquired", label: "Year Acquired" },
                
                // Data Entry Information
                { fieldName: "name_encoder", label: "Encoder" },
                { fieldName: "email", label: "Email" },
                { fieldName: "remarks", label: "Remarks" }
            ]
        },
        tableTemplate: {
            columnTemplates: [
                // Key Building Information (most important columns first)
                { type: "field", fieldName: "name_building", label: "Building Name" },
                { type: "field", fieldName: "facility_code", label: "Facility Code" },
                { type: "field", fieldName: "building_purpose", label: "Building Purpose" },
                { type: "field", fieldName: "region", label: "Region" },
                { type: "field", fieldName: "bureau", label: "Bureau" },
                { type: "field", fieldName: "office_level_1", label: "Office Level 1" },
                { type: "field", fieldName: "office_level_2", label: "Office Level 2" },
                
                // Physical Information
                { type: "field", fieldName: "street_address", label: "Street Address" },
                { type: "field", fieldName: "floor_area", label: "Floor Area (sq.m)" },
                { type: "field", fieldName: "land_area", label: "Land Area (sq.m)" },
                { type: "field", fieldName: "number_floor", label: "Floors" },
                { type: "field", fieldName: "max_occupants", label: "Max Occupants" },
                { type: "field", fieldName: "year_constructed", label: "Year Built" },
                { type: "field", fieldName: "buildingvalue", label: "Building Value" },
                
                // Ownership & Administrative
                { type: "field", fieldName: "registered_owner", label: "Owner" },
                { type: "field", fieldName: "ownership_type", label: "Ownership Type" },
                { type: "field", fieldName: "year_acquired", label: "Year Acquired" },
                { type: "field", fieldName: "penro", label: "PENRO" },
                { type: "field", fieldName: "cenro", label: "CENRO" },
                
                // Additional Information
                { type: "field", fieldName: "facility_id", label: "Facility ID" },
                { type: "field", fieldName: "building_purpose_others", label: "Other Purpose" },
                { type: "field", fieldName: "name_encoder", label: "Encoder" },
                { type: "field", fieldName: "email", label: "Email" },
                { type: "field", fieldName: "remarks", label: "Remarks" }
            ]
        }
    },

    /**
     * Load configuration from localStorage or use defaults
     * @returns {Object} Configuration object
     */
    load() {
        try {
            const stored = localStorage.getItem('erdb-config');
            if (stored) {
                const config = JSON.parse(stored);
                
                // Auto-fix problematic portal URL with /home suffix
                if (config.services && config.services.portalUrl && 
                    config.services.portalUrl.includes('/home')) {
                    console.warn('Detected problematic portal URL with /home suffix, auto-correcting...');
                    config.services.portalUrl = config.services.portalUrl.replace('/home', '');
                    localStorage.setItem('erdb-config', JSON.stringify(config));
                    console.log('Portal URL auto-corrected to:', config.services.portalUrl);
                }
                
                // Auto-fix MapServer URLs to FeatureServer
                if (config.services && config.services.featureLayerUrl && 
                    config.services.featureLayerUrl.includes('MapServer')) {
                    console.warn('Detected MapServer URL, auto-correcting to FeatureServer...');
                    config.services.featureLayerUrl = config.services.featureLayerUrl.replace('MapServer', 'FeatureServer');
                    localStorage.setItem('erdb-config', JSON.stringify(config));
                    console.log('Feature Layer URL auto-corrected to:', config.services.featureLayerUrl);
                }
                
                console.log('Configuration loaded from localStorage');
                return { ...this.default, ...config };
            }
        } catch (error) {
            console.warn('Error loading stored configuration:', error.message);
        }
        console.log('Using default configuration');
        return { ...this.default };
    },

    /**
     * Save configuration to localStorage with security enhancements
     * @param {Object} config - Configuration to save
     * @returns {Promise<boolean>} Success status
     */
    async save(config) {
        try {
            // Validate configuration before saving
            const validatedConfig = this.validateConfiguration(config);
            
            // Encrypt sensitive authentication data if SecurityUtils is available
            if (typeof SecurityUtils !== 'undefined' && validatedConfig.authentication && validatedConfig.authentication.password) {
                const deviceId = await this.getDeviceId();
                const encryptedPassword = await SecurityUtils.encryptData(
                    validatedConfig.authentication.password, 
                    deviceId
                );
                validatedConfig.authentication.password = encryptedPassword;
                validatedConfig.authentication.encrypted = true;
            }
            
            localStorage.setItem('erdb-config', JSON.stringify(validatedConfig));
            console.log('Configuration saved securely to localStorage');
            return true;
        } catch (error) {
            console.error('Error saving configuration:', error.message);
            return false;
        }
    },

    /**
     * Validate configuration object
     * @param {Object} config - Configuration to validate
     * @returns {Object} Validated configuration
     */
    validateConfiguration(config) {
        const validated = { ...config };
        
        // Use SecurityUtils if available, otherwise basic validation
        const sanitizer = typeof SecurityUtils !== 'undefined' ? SecurityUtils.validateAndSanitize : {
            text: (input, maxLength = 1000) => input ? String(input).trim().substring(0, maxLength) : '',
            url: (input) => {
                try {
                    const url = new URL(input);
                    return ['http:', 'https:'].includes(url.protocol) ? url.href : null;
                } catch {
                    return null;
                }
            },
            number: (input, min = -Infinity, max = Infinity) => {
                const num = parseFloat(input);
                return isNaN(num) ? null : Math.max(min, Math.min(max, num));
            },
            coordinates: (input) => {
                try {
                    const coords = Array.isArray(input) ? input : JSON.parse(input);
                    if (!Array.isArray(coords) || coords.length !== 2) return null;
                    const [lng, lat] = coords.map(c => parseFloat(c));
                    if (isNaN(lng) || isNaN(lat)) return null;
                    return [Math.max(-180, Math.min(180, lng)), Math.max(-90, Math.min(90, lat))];
                } catch {
                    return null;
                }
            }
        };
        
        // Validate app settings
        if (validated.app) {
            validated.app.title = sanitizer.text(validated.app.title, 100);
            validated.app.description = sanitizer.text(validated.app.description, 500);
        }
        
        // Validate map settings
        if (validated.map) {
            validated.map.center = sanitizer.coordinates(validated.map.center) || [122, 12];
            validated.map.zoom = sanitizer.number(validated.map.zoom, 1, 20) || 6;
        }
        
        // Validate service URLs
        if (validated.services) {
            validated.services.portalUrl = sanitizer.url(validated.services.portalUrl);
            validated.services.featureLayerUrl = sanitizer.url(validated.services.featureLayerUrl);
        }
        
        // Validate authentication settings
        if (validated.authentication) {
            validated.authentication.appId = sanitizer.text(validated.authentication.appId, 100);
            validated.authentication.username = sanitizer.text(validated.authentication.username, 100);
        }
        
        return validated;
    },

    /**
     * Get device-specific identifier for encryption
     * @returns {Promise<string>} Device ID
     */
    async getDeviceId() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Device fingerprint', 2, 2);
            
            const fingerprint = canvas.toDataURL() + 
                             navigator.userAgent + 
                             navigator.language + 
                             screen.width + 'x' + screen.height;
            
            const encoder = new TextEncoder();
            const data = encoder.encode(fingerprint);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        } catch (error) {
            console.warn('Could not generate device ID:', error);
            return 'default-device-id';
        }
    },

    /**
     * Reset to defaults
     * @returns {boolean} Success status
     */
    reset() {
        try {
            localStorage.removeItem('erdb-config');
            console.log('Configuration reset to defaults');
            return true;
        } catch (error) {
            console.error('Error resetting configuration:', error.message);
            return false;
        }
    },

    /**
     * Apply URL parameter overrides
     * @param {Object} config - Base configuration
     * @returns {Object} Configuration with URL overrides
     */
    applyUrlOverrides(config) {
        const urlParams = new URLSearchParams(window.location.search);
        
        try {
            if (urlParams.get('portalUrl')) {
                config.services.portalUrl = urlParams.get('portalUrl');
                console.log('Override: portalUrl =', config.services.portalUrl);
            }
            if (urlParams.get('featureLayerUrl')) {
                config.services.featureLayerUrl = urlParams.get('featureLayerUrl');
                console.log('Override: featureLayerUrl =', config.services.featureLayerUrl);
            }
            if (urlParams.get('basemap')) {
                config.map.basemap = urlParams.get('basemap');
                console.log('Override: basemap =', config.map.basemap);
            }
            if (urlParams.get('center')) {
                config.map.center = JSON.parse(urlParams.get('center'));
                console.log('Override: center =', config.map.center);
            }
            if (urlParams.get('zoom')) {
                config.map.zoom = parseInt(urlParams.get('zoom'));
                console.log('Override: zoom =', config.map.zoom);
            }
            if (urlParams.get('appId')) {
                config.authentication.appId = urlParams.get('appId');
                console.log('Override: appId =', config.authentication.appId);
            }
        } catch (paramError) {
            console.warn('Error processing URL parameters:', paramError.message);
        }
        
        return config;
    },

    /**
     * Auto-discover fields from feature layer URL
     * @param {string} layerUrl - Feature layer URL
     * @param {string} authToken - Optional authentication token
     * @returns {Promise<Object>} Discovered configuration
     */
    async discoverFields(layerUrl, authToken = null) {
        try {
            console.log('Discovering fields from:', layerUrl);
            
            let serviceUrl = layerUrl;
            if (!serviceUrl.endsWith('/')) {
                serviceUrl += '/';
            }
            
            const urlParams = new URLSearchParams();
            urlParams.append('f', 'json');
            
            if (authToken) {
                urlParams.append('token', authToken);
                console.log('Using authentication token for field discovery');
            }
            
            serviceUrl += '?' + urlParams.toString();

            const response = await fetch(serviceUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    throw new Error('Authentication required. Please check your credentials or ensure the service is publicly accessible.');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const layerInfo = await response.json();
            
            if (layerInfo.error) {
                throw new Error(layerInfo.error.message || 'Service returned an error');
            }

            if (!layerInfo.fields || !Array.isArray(layerInfo.fields)) {
                throw new Error('No field information found in service response');
            }

            // Filter out system fields and create configurations
            const systemFields = ['OBJECTID', 'SHAPE', 'SHAPE_Length', 'SHAPE_Area', 'GlobalID', 'created_user', 'created_date', 'last_edited_user', 'last_edited_date'];
            const userFields = layerInfo.fields.filter(field => 
                !systemFields.includes(field.name) && 
                !field.name.startsWith('SHAPE') &&
                field.type !== 'esriFieldTypeGeometry' &&
                field.type !== 'esriFieldTypeOID'
            );

            const tableColumns = userFields.map(field => ({
                type: "field",
                fieldName: field.name,
                label: field.alias || field.name,
                direction: "asc"
            }));

            const popupFields = userFields.map(field => ({
                fieldName: field.name,
                label: field.alias || field.name,
                visible: true
            }));

            const titleField = layerInfo.displayField || 
                             (popupFields.length > 0 ? popupFields[0].fieldName : 'OBJECTID');

            const discoveredConfig = {
                tableTemplate: {
                    columnTemplates: tableColumns
                },
                popupTemplate: {
                    title: `{${titleField}}`,
                    fieldInfos: popupFields
                },
                layerInfo: {
                    name: layerInfo.name || 'Feature Layer',
                    description: layerInfo.description || '',
                    geometryType: layerInfo.geometryType || '',
                    fields: layerInfo.fields,
                    capabilities: layerInfo.capabilities || ''
                }
            };

            console.log('Field discovery successful:', discoveredConfig);
            return discoveredConfig;

        } catch (error) {
            console.error('Error discovering fields:', error);
            throw error;
        }
    }
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppConfig;
}

// Make available globally for inline script use
if (typeof window !== 'undefined') {
    window.AppConfig = AppConfig;
}
