<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERDB Control Map - Fix Validation Tests</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .test-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .summary {
            font-size: 1.2em;
            text-align: center;
            padding: 20px;
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 ERDB Control Map - Fix Validation Tests</h1>
    <p>This page validates all the critical fixes applied to the ERDB Control Map codebase.</p>

    <div class="test-container">
        <h2>🚀 Quick Test</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="runConfigurationTests()">Test Configuration</button>
        <button onclick="runSecurityTests()">Test Security</button>
        <button onclick="runModuleTests()">Test Modules</button>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>📋 Test Results Summary</h2>
        <div id="summary"></div>
    </div>

    <div class="test-container">
        <h2>🔧 Manual Tests</h2>
        <p>These tests require manual verification:</p>
        <ul>
            <li><strong>Authentication Flow:</strong> <a href="index.html" target="_blank">Open main app</a> and test sign-in/sign-out</li>
            <li><strong>Configuration Interface:</strong> <a href="config.html" target="_blank">Open config page</a> and test settings</li>
            <li><strong>Error Recovery:</strong> Simulate network errors and verify recovery mechanisms</li>
            <li><strong>Memory Management:</strong> Monitor browser dev tools for memory leaks</li>
        </ul>
    </div>

    <script src="js/security.js"></script>
    <script src="js/app-config.js"></script>
    <script src="js/module-loader.js"></script>
    <script src="js/error-manager.js"></script>
    <script src="js/auth-manager.js"></script>

    <script>
        let testResults = [];

        function addTestResult(testName, passed, message, details = '') {
            testResults.push({
                name: testName,
                passed: passed,
                message: message,
                details: details
            });
            
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
            resultDiv.innerHTML = `
                <strong>${passed ? '✅' : '❌'} ${testName}</strong><br>
                ${message}
                ${details ? `<div class="code-block">${details}</div>` : ''}
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function runConfigurationTests() {
            console.log('🔧 Running configuration tests...');
            
            // Test 1: Configuration consistency
            try {
                const config = AppConfig.load();
                const expectedTitle = "DENR Control Map";
                const actualTitle = config.app.title;
                
                if (actualTitle === expectedTitle) {
                    addTestResult(
                        'Configuration Consistency', 
                        true, 
                        'App title matches expected value',
                        `Expected: "${expectedTitle}"\nActual: "${actualTitle}"`
                    );
                } else {
                    addTestResult(
                        'Configuration Consistency', 
                        false, 
                        'App title mismatch detected',
                        `Expected: "${expectedTitle}"\nActual: "${actualTitle}"`
                    );
                }
            } catch (error) {
                addTestResult(
                    'Configuration Consistency', 
                    false, 
                    'Failed to load configuration',
                    error.message
                );
            }

            // Test 2: URL validation
            try {
                const testUrl = "https://controlmap.denr.gov.ph/arcgis";
                const validatedUrl = SecurityUtils.validateAndSanitize.url(testUrl);
                
                addTestResult(
                    'URL Validation', 
                    validatedUrl === testUrl, 
                    'URL validation working correctly',
                    `Input: ${testUrl}\nOutput: ${validatedUrl}`
                );
            } catch (error) {
                addTestResult(
                    'URL Validation', 
                    false, 
                    'URL validation failed',
                    error.message
                );
            }
        }

        function runSecurityTests() {
            console.log('🔒 Running security tests...');
            
            // Test 1: Text sanitization
            try {
                const maliciousInput = '<script>alert("xss")</script>Hello';
                const sanitized = SecurityUtils.validateAndSanitize.text(maliciousInput);
                const isSafe = !sanitized.includes('<script>');
                
                addTestResult(
                    'XSS Protection', 
                    isSafe, 
                    'Text sanitization working correctly',
                    `Input: ${maliciousInput}\nSanitized: ${sanitized}`
                );
            } catch (error) {
                addTestResult(
                    'XSS Protection', 
                    false, 
                    'Text sanitization failed',
                    error.message
                );
            }

            // Test 2: Coordinate validation
            try {
                const validCoords = [121.0244, 14.6349];
                const invalidCoords = [999, 999];
                
                const validResult = SecurityUtils.validateAndSanitize.coordinates(validCoords);
                const invalidResult = SecurityUtils.validateAndSanitize.coordinates(invalidCoords);
                
                const testPassed = validResult !== null && invalidResult !== null;
                
                addTestResult(
                    'Coordinate Validation', 
                    testPassed, 
                    'Coordinate validation working correctly',
                    `Valid coords: ${JSON.stringify(validCoords)} → ${JSON.stringify(validResult)}\nInvalid coords: ${JSON.stringify(invalidCoords)} → ${JSON.stringify(invalidResult)}`
                );
            } catch (error) {
                addTestResult(
                    'Coordinate Validation', 
                    false, 
                    'Coordinate validation failed',
                    error.message
                );
            }
        }

        function runModuleTests() {
            console.log('📦 Running module tests...');
            
            // Test 1: Module availability
            const modules = {
                'SecurityUtils': typeof SecurityUtils !== 'undefined',
                'AppConfig': typeof AppConfig !== 'undefined',
                'ModuleLoader': typeof ModuleLoader !== 'undefined',
                'ErrorManager': typeof ErrorManager !== 'undefined',
                'AuthManager': typeof AuthManager !== 'undefined'
            };
            
            const allModulesLoaded = Object.values(modules).every(loaded => loaded);
            
            addTestResult(
                'Module Loading', 
                allModulesLoaded, 
                allModulesLoaded ? 'All modules loaded successfully' : 'Some modules missing',
                Object.entries(modules).map(([name, loaded]) => `${name}: ${loaded ? '✅' : '❌'}`).join('\n')
            );

            // Test 2: Error Manager initialization
            try {
                const errorStats = ErrorManager.getErrorStats();
                const isInitialized = typeof errorStats === 'object' && errorStats.hasOwnProperty('totalErrors');
                
                addTestResult(
                    'Error Manager Initialization', 
                    isInitialized, 
                    'Error Manager initialized correctly',
                    `Error stats available: ${isInitialized}\nTotal errors: ${errorStats.totalErrors || 0}`
                );
            } catch (error) {
                addTestResult(
                    'Error Manager Initialization', 
                    false, 
                    'Error Manager initialization failed',
                    error.message
                );
            }
        }

        function runAllTests() {
            // Clear previous results
            document.getElementById('test-results').innerHTML = '';
            testResults = [];
            
            console.log('🚀 Running comprehensive test suite...');
            
            runConfigurationTests();
            runSecurityTests();
            runModuleTests();
            
            // Generate summary
            setTimeout(() => {
                generateSummary();
            }, 100);
        }

        function generateSummary() {
            const totalTests = testResults.length;
            const passedTests = testResults.filter(test => test.passed).length;
            const failedTests = totalTests - passedTests;
            
            const summaryDiv = document.getElementById('summary');
            const successRate = ((passedTests / totalTests) * 100).toFixed(1);
            
            summaryDiv.innerHTML = `
                <div class="summary ${failedTests === 0 ? 'test-pass' : 'test-warning'}">
                    <h3>Test Results Summary</h3>
                    <p><strong>Total Tests:</strong> ${totalTests}</p>
                    <p><strong>Passed:</strong> ${passedTests}</p>
                    <p><strong>Failed:</strong> ${failedTests}</p>
                    <p><strong>Success Rate:</strong> ${successRate}%</p>
                    ${failedTests === 0 ? 
                        '<p>🎉 All tests passed! The fixes are working correctly.</p>' : 
                        '<p>⚠️ Some tests failed. Please review the results above.</p>'
                    }
                </div>
            `;
            
            console.log(`📊 Test Summary: ${passedTests}/${totalTests} tests passed (${successRate}%)`);
        }

        // Auto-run tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
