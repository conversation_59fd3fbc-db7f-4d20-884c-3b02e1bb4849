<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERDB Control Map - Configuration</title>
    <!-- Prevent favicon 500 errors by providing a data URL favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚙️</text></svg>">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 5px solid #4CAF50;
        }
        
        .section h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.3em;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
            font-size: 0.95em;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        
        .buttons {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 8px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }
        
        .btn.secondary {
            background: #2196F3;
        }
        
        .btn.secondary:hover {
            background: #1976D2;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }
        
        .status-message {
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: 500;
            display: none;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .help-text {
            font-size: 0.85em;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .navigation {
            background: #34495e;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .navigation a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background 0.3s ease;
        }
        
        .navigation a:hover {
            background: rgba(255,255,255,0.1);
        }
        
        @media (max-width: 768px) {
            .grid-2, .grid-3 {
                grid-template-columns: 1fr;
            }
            
            body {
                padding: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="navigation">
            <div>
                <a href="index.html">← Back to Map</a>
            </div>
            <div>
                <span style="color: #bdc3c7;">ERDB Control Map Configuration</span>
            </div>
        </div>
        
        <div class="header">
            <h1>⚙️ Configuration Manager</h1>
            <p>Customize your ERDB Control Map settings</p>
        </div>
        
        <div class="content">
            <div id="status-message" class="status-message"></div>
            
            <div class="section">
                <h3>🏢 Application Settings</h3>
                <div class="grid-2">
                    <div class="form-group">
                        <label for="cfg-title">Application Title</label>
                        <input type="text" id="cfg-title" placeholder="ERDB Control Map">
                        <div class="help-text">This appears in the browser tab and application header</div>
                    </div>
                    <div class="form-group">
                        <label for="cfg-description">Description</label>
                        <input type="text" id="cfg-description" placeholder="Application description">
                        <div class="help-text">Shown in the navigation area</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>🗺️ Map Configuration</h3>
                <div class="grid-3">
                    <div class="form-group">
                        <label for="cfg-center-lng">Center Longitude</label>
                        <input type="number" id="cfg-center-lng" step="0.000001" placeholder="122.0">
                        <div class="help-text">East/West position</div>
                    </div>
                    <div class="form-group">
                        <label for="cfg-center-lat">Center Latitude</label>
                        <input type="number" id="cfg-center-lat" step="0.000001" placeholder="12.0">
                        <div class="help-text">North/South position</div>
                    </div>
                    <div class="form-group">
                        <label for="cfg-zoom">Zoom Level</label>
                        <input type="number" id="cfg-zoom" min="1" max="20" placeholder="6">
                        <div class="help-text">1 = World view, 20 = Street level</div>
                    </div>
                </div>
                <div class="grid-2">
                    <div class="form-group">
                        <label for="cfg-basemap">Default Basemap</label>
                        <select id="cfg-basemap">
                            <option value="topo">Topographic</option>
                            <option value="streets">Streets</option>
                            <option value="satellite">Satellite</option>
                            <option value="hybrid">Hybrid</option>
                            <option value="terrain">Terrain</option>
                            <option value="osm">OpenStreetMap</option>
                            <option value="dark-gray">Dark Gray</option>
                            <option value="gray">Gray</option>
                        </select>
                        <div class="help-text">Background map style</div>
                    </div>
                    <div class="form-group">
                        <label for="cfg-alt-basemap">Toggle Basemap</label>
                        <select id="cfg-alt-basemap">
                            <option value="satellite">Satellite</option>
                            <option value="topo">Topographic</option>
                            <option value="streets">Streets</option>
                            <option value="hybrid">Hybrid</option>
                            <option value="terrain">Terrain</option>
                            <option value="osm">OpenStreetMap</option>
                            <option value="dark-gray">Dark Gray</option>
                            <option value="gray">Gray</option>
                        </select>
                        <div class="help-text">Alternate basemap for toggle button</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>🌐 Service Configuration</h3>
                <div class="form-group">
                    <label for="cfg-portal-url">Portal URL</label>
                    <input type="url" id="cfg-portal-url" placeholder="https://controlmap.denr.gov.ph/arcgis">
                    <div class="help-text">Your ArcGIS Portal URL (without /home suffix)</div>
                </div>
                <div class="form-group">
                    <label for="cfg-feature-url">Feature Layer URL</label>
                    <input type="url" id="cfg-feature-url" placeholder="https://server/rest/services/layer/FeatureServer">
                    <div class="help-text">URL to your feature service for data display and editing</div>
                </div>
            </div>

            <div class="buttons">
                <button class="btn" onclick="saveConfiguration()">💾 Save & Apply</button>
                <button class="btn secondary" onclick="loadConfigurationForm()">🔄 Reload Form</button>
                <button class="btn secondary" onclick="testConfiguration()">🧪 Test Settings</button>
            </div>
            
            <div class="buttons">
                <a href="index.html" class="btn secondary">🗺️ Open Map Application</a>
                <a href="index.html?debug=true" class="btn secondary">🔍 Open with Debug Mode</a>
            </div>
        </div>
    </div>

    <!-- Module loading scripts -->
    <script src="js/module-loader.js"></script>
    <script src="js/security.js"></script>
    <script src="js/app-config.js"></script>
    <script src="js/error-manager.js"></script>

    <script>
        // Configuration management using shared modules
        
        // Load configuration form with current values
        function loadConfigurationForm() {
            const config = AppConfig.load();
            
            // Load values into form
            document.getElementById('cfg-title').value = config.app.title || '';
            document.getElementById('cfg-description').value = config.app.description || '';
            document.getElementById('cfg-center-lng').value = config.map.center[0] || '';
            document.getElementById('cfg-center-lat').value = config.map.center[1] || '';
            document.getElementById('cfg-zoom').value = config.map.zoom || '';
            document.getElementById('cfg-basemap').value = config.map.basemap || '';
            document.getElementById('cfg-alt-basemap').value = config.map.alternateBasemap || '';
            document.getElementById('cfg-portal-url').value = config.services.portalUrl || '';
            document.getElementById('cfg-feature-url').value = config.services.featureLayerUrl || '';
            
            showStatus('Configuration loaded', 'success');
        }

        // Save configuration with validation
        async function saveConfiguration() {
            try {
                // Get values from form with validation
                const newConfig = {
                    app: {
                        title: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-title').value),
                        description: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-description').value),
                        logoHeading: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-title').value),
                        signOutDescription: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-description').value) + " To sign out, click on the logged in user button.",
                        signInDescription: "Use OAuth to log in to an ArcGIS Organization to view your items."
                    },
                    map: {
                        center: [
                            SecurityUtils.validateAndSanitize.number(document.getElementById('cfg-center-lng').value, -180, 180) || 122,
                            SecurityUtils.validateAndSanitize.number(document.getElementById('cfg-center-lat').value, -90, 90) || 12
                        ],
                        zoom: SecurityUtils.validateAndSanitize.number(document.getElementById('cfg-zoom').value, 1, 20) || 6,
                        basemap: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-basemap').value, 50),
                        alternateBasemap: SecurityUtils.validateAndSanitize.text(document.getElementById('cfg-alt-basemap').value, 50)
                    },
                    services: {
                        portalUrl: SecurityUtils.validateAndSanitize.url(document.getElementById('cfg-portal-url').value),
                        featureLayerUrl: SecurityUtils.validateAndSanitize.url(document.getElementById('cfg-feature-url').value)
                    }
                };

                // Validate URLs
                if (newConfig.services.portalUrl === null) {
                    showStatus('Invalid Portal URL. Please enter a valid HTTPS URL.', 'error');
                    return;
                }
                
                if (newConfig.services.featureLayerUrl === null) {
                    showStatus('Invalid Feature Layer URL. Please enter a valid HTTPS URL.', 'error');
                    return;
                }

                // Merge with defaults to ensure all properties exist
                const fullConfig = { ...AppConfig.default, ...newConfig };
                
                // Save configuration
                const saveResult = await AppConfig.save(fullConfig);
                if (saveResult) {
                    showStatus('✅ Configuration saved successfully! Changes will apply when you reload the map application.', 'success');
                } else {
                    showStatus('❌ Error saving configuration. Please try again.', 'error');
                }
            } catch (error) {
                showStatus('❌ Error saving configuration: ' + error.message, 'error');
                console.error('Configuration save error:', error);
            }
        }

        // Test configuration by opening map with current settings
        function testConfiguration() {
            const testUrl = 'index.html?' + new URLSearchParams({
                basemap: document.getElementById('cfg-basemap').value,
                center: `[${document.getElementById('cfg-center-lng').value},${document.getElementById('cfg-center-lat').value}]`,
                zoom: document.getElementById('cfg-zoom').value,
                debug: 'true'
            }).toString();
            
            window.open(testUrl, '_blank');
            showStatus('Opening test window with current settings...', 'success');
        }

        // Show status message
        function showStatus(message, type) {
            const statusEl = document.getElementById('status-message');
            statusEl.textContent = message;
            statusEl.className = `status-message status-${type}`;
            statusEl.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 5000);
        }

        // Initialize configuration interface
        function initializeConfiguration() {
            console.log('🔧 Initializing modular configuration interface...');
            
            // Check if modules are available
            if (typeof SecurityUtils === 'undefined') {
                console.error('SecurityUtils module not available');
                showStatus('Error: Security module not loaded', 'error');
                return;
            }
            
            if (typeof AppConfig === 'undefined') {
                console.error('AppConfig module not available');
                showStatus('Error: Configuration module not loaded', 'error');
                return;
            }
            
            // Load the configuration form
            loadConfigurationForm();
            
            console.log('✅ Modular configuration interface ready');
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                // Check if modules are bundled, otherwise load them dynamically
                if (!window.ERDB_BUNDLED_MODE && typeof ModuleLoader !== 'undefined') {
                    ModuleLoader.initializeApp(['security', 'app-config', 'error-manager'], initializeConfiguration);
                } else {
                    // Bundled mode or modules already loaded - proceed directly
                    initializeConfiguration();
                }
            });
        } else {
            // DOM is already ready, initialize immediately
            if (!window.ERDB_BUNDLED_MODE && typeof ModuleLoader !== 'undefined') {
                ModuleLoader.initializeApp(['security', 'app-config', 'error-manager'], initializeConfiguration);
            } else {
                initializeConfiguration();
            }
        }
    </script>
</body>
</html>
