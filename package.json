{"name": "erdb-control-map", "version": "2.0.0", "description": "ERDB Control Map - Administrative Buildings Management System", "main": "index.html", "scripts": {"build": "node build.js", "dev": "echo 'Open index.html in browser for development mode'", "clean": "rm -rf dist/", "test": "echo 'No tests configured yet'", "lint": "echo 'Install ESLint for code quality checks'"}, "keywords": ["<PERSON><PERSON>", "mapping", "buildings", "administrative", "denr", "gis", "javascript"], "author": "DENR ERDB Team", "license": "MIT", "devDependencies": {}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "local"}, "dependencies": {}}